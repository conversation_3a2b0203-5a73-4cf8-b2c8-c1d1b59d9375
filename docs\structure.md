# SGF Portal - Comprehensive Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Technology Stack](#technology-stack)
4. [Database Schema](#database-schema)
5. [User Management System](#user-management-system)
6. [Project Management Features](#project-management-features)
7. [Task Management System](#task-management-system)
8. [Calendar & Resource Allocation](#calendar--resource-allocation)
9. [Comments & Communication System](#comments--communication-system)
10. [API Documentation](#api-documentation)
11. [Frontend Components](#frontend-components)
12. [Installation & Setup](#installation--setup)
13. [Configuration](#configuration)
14. [Troubleshooting](#troubleshooting)

---

## Project Overview

### About SGF Portal

SGF Portal is a comprehensive project management system built specifically for Shields SGF LLC. It provides a complete solution for managing projects, tasks, teams, clients, and resources with real-time collaboration features.

### Key Features

- **Multi-tenant Project Management**: Support for multiple brands, clients, and projects
- **Advanced Task Management**: Task creation, assignment, status tracking, and collaboration
- **Real-time Communication**: Live comments system with file attachments and user mentions
- **Resource Allocation**: Track team utilization and resource distribution across projects
- **Calendar Integration**: Project timelines, deadlines, holidays, and team schedules
- **Role-based Access Control**: Comprehensive permission system with multiple user roles
- **File Management**: Upload, preview, and manage project files and attachments
- **Metrics & Reporting**: Essential metrics tracking and PDF report generation
- **Responsive Design**: Mobile-friendly interface with dark theme

### Business Context

The system is designed to handle the complete project lifecycle from client onboarding to project delivery, with specific focus on:

- **Client Management**: Multiple clients with associated brands and projects
- **Project Phases**: Structured project workflow (Define, Design, Development, Testing, Deploy, Manage)
- **Team Collaboration**: Real-time communication and file sharing
- **Resource Planning**: Weekly resource allocation and utilization tracking
- **Timeline Management**: Project deadlines and milestone tracking

---

## System Architecture

### Application Structure

SGF Portal follows Laravel's MVC (Model-View-Controller) architecture with additional layers:

```
SGF-Portal/
├── app/
│   ├── Http/Controllers/          # Request handling logic
│   ├── Models/                    # Database models and relationships
│   ├── Livewire/                  # Interactive frontend components
│   ├── Notifications/             # Email and system notifications
│   ├── Events/                    # Real-time event broadcasting
│   └── Helpers/                   # Utility functions
├── database/
│   ├── migrations/                # Database schema definitions
│   └── seeders/                   # Initial data population
├── resources/
│   ├── views/                     # Blade templates
│   ├── css/                       # Stylesheets
│   └── js/                        # JavaScript files
├── public/
│   ├── css/                       # Compiled CSS
│   ├── js/                        # JavaScript libraries
│   └── images/                    # Static assets
└── routes/                        # Application routes
```

### Core Components

1. **Authentication System**: Laravel's built-in authentication with custom role management
2. **Real-time Features**: Laravel Reverb for WebSocket connections
3. **Interactive UI**: Livewire components for dynamic frontend interactions
4. **File Handling**: Custom file upload and management system
5. **Notification System**: Email notifications and real-time alerts
6. **Permission System**: Spatie Laravel Permission package for role-based access

---

## Technology Stack

### Backend Technologies

- **Framework**: Laravel 12.0 (PHP 8.2+)
- **Database**: MySQL with Eloquent ORM
- **Real-time**: Laravel Reverb (WebSocket server)
- **Queue System**: Laravel Queue for background jobs
- **Authentication**: Laravel Sanctum for API authentication
- **Permissions**: Spatie Laravel Permission package
- **PDF Generation**: Spatie Laravel PDF package

### Frontend Technologies

- **CSS Framework**: Bootstrap 5.3.3
- **JavaScript**: jQuery 3.6.4
- **Interactive Components**: Livewire 3.6
- **Rich Text Editor**: TinyMCE
- **Icons**: Font Awesome 6.7.2
- **Build Tool**: Vite 6.3.3
- **Styling**: Custom SCSS with Tailwind CSS 4.0

### Development Tools

- **Package Manager**: Composer (PHP), NPM (JavaScript)
- **Code Quality**: Laravel Pint, PHPStan
- **Testing**: PHPUnit, Pest
- **Version Control**: Git

### Third-party Services

- **Email**: Laravel Mail system
- **File Storage**: Local filesystem with Laravel Storage
- **Logging**: Laravel Pail for log monitoring
- **Error Tracking**: Laravel's built-in error handling

---

## Database Schema

### Core Tables

#### Users Table

```sql
users (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    email VARCHAR(255) UNIQUE,
    password VARCHAR(255),
    role_id BIGINT FOREIGN KEY -> roles(id),
    access_level_id BIGINT FOREIGN KEY -> access_level(id),
    profile_image VARCHAR(255) NULLABLE,
    color_code VARCHAR(255) NULLABLE,
    email_verified_at TIMESTAMP NULLABLE,
    remember_token VARCHAR(100) NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Projects Table

```sql
projects (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    job_code VARCHAR(255),
    status VARCHAR(255),
    client_id BIGINT FOREIGN KEY -> clients(id),
    timeline_id BIGINT FOREIGN KEY -> timelines(id),
    harvest_link VARCHAR(255) NULLABLE,
    invoice_schedule VARCHAR(255) NULLABLE,
    kickoff_message_id BIGINT FOREIGN KEY -> project_message_threads(id),
    pm_hours INTEGER NULLABLE,
    designer_hours INTEGER NULLABLE,
    developer_hours INTEGER NULLABLE,
    cs_hours INTEGER NULLABLE,
    deleted_at TIMESTAMP NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Tasks Table

```sql
tasks (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    description TEXT NULLABLE,
    project_id BIGINT FOREIGN KEY -> projects(id),
    status_id BIGINT FOREIGN KEY -> status(id),
    due_date VARCHAR(255) NULLABLE,
    admin_id BIGINT FOREIGN KEY -> users(id),
    attachemenet VARCHAR(255) NULLABLE,
    deleted_at TIMESTAMP NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

### Relationship Tables

#### Project-User Relationships

```sql
project_user (
    id BIGINT PRIMARY KEY,
    project_id BIGINT FOREIGN KEY -> projects(id),
    user_id BIGINT FOREIGN KEY -> users(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Task-User Relationships

```sql
task_user (
    id BIGINT PRIMARY KEY,
    task_id BIGINT FOREIGN KEY -> tasks(id),
    user_id BIGINT FOREIGN KEY -> users(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Project Phases

```sql
project_phase (
    id BIGINT PRIMARY KEY,
    project_id BIGINT FOREIGN KEY -> projects(id),
    phase_id BIGINT FOREIGN KEY -> phases(id),
    project_duration VARCHAR(255) NULLABLE,
    project_target VARCHAR(255) NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

### Supporting Tables

#### Roles & Permissions

```sql
roles (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) DEFAULT 'User',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

permissions (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

role_permissions (
    id BIGINT PRIMARY KEY,
    permission_id BIGINT FOREIGN KEY -> permissions(id),
    role_id BIGINT FOREIGN KEY -> roles(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Clients & Brands

```sql
clients (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) UNIQUE,
    job_code VARCHAR(255),
    user_id BIGINT FOREIGN KEY -> users(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

brands (
    id BIGINT PRIMARY KEY,
    admin_id BIGINT FOREIGN KEY -> users(id),
    job_code VARCHAR(255),
    name VARCHAR(255) NULLABLE,
    description TEXT NULLABLE,
    logo VARCHAR(255) NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

client_brand (
    id BIGINT PRIMARY KEY,
    client_id BIGINT FOREIGN KEY -> clients(id),
    brand_id BIGINT FOREIGN KEY -> brands(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Comments System

```sql
comments (
    id BIGINT PRIMARY KEY,
    user_id BIGINT FOREIGN KEY -> users(id),
    task_id BIGINT FOREIGN KEY -> tasks(id),
    comment_parent_id BIGINT FOREIGN KEY -> comments(id),
    comment_body TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

---

## User Management System

### User Roles

The system implements a comprehensive role-based access control system with the following roles:

1. **SuperAdmin**

   - Full system access
   - User management capabilities
   - System configuration
   - All project and task permissions

2. **Admin**

   - User management within assigned brands
   - Project creation and management
   - Task assignment and oversight
   - Resource allocation management

3. **Team Member**

   - The primary role for most users in the system
   - Can have different access levels (Admin or Team Member)
   - Task management and completion
   - File uploads and comments
   - Project participation based on assignments

4. **Client**

   - Basic user role
   - Limited system access
   - Project-specific communication
   - File downloads for approved deliverables

5. **Brand Member**

   - Oversees projects for their specific brand/enterprise
   - Brand-wide project monitoring
   - Multi-client project management
   - Phase-based project organization

### Access Levels

The system uses a two-tier access level system for Team Members:

1. **Admin Access Level**

   - Team Members with Admin access level get Admin-level permissions
   - Can manage users, projects, and tasks like Admin role
   - Determined by `access_level_id` field in users table

2. **Team Member Access Level**
   - Standard Team Member permissions
   - Limited to assigned projects and tasks
   - Cannot manage other users or system settings

### Permission System

The permission system works as follows:

- **SuperAdmin/Admin roles**: Get permissions based on their role
- **Team Member role**: Permissions determined by access_level_id
  - If access_level_id = Admin → gets Admin permissions
  - If access_level_id = Team Member → gets Team Member permissions
- **Dynamic Permission Assignment**: Team Members can be elevated to Admin access without changing their role

### User Authentication

- **Laravel Sanctum**: API token-based authentication
- **Session Management**: Web-based session handling
- **Password Security**: Hashed passwords with Laravel's built-in security
- **Email Verification**: Optional email verification system
- **Remember Token**: Persistent login functionality

### User Profile Management

Each user profile includes:

- **Personal Information**: Name, email, profile image
- **Role Assignment**: Primary role and access level
- **Color Coding**: User-specific color for UI identification
- **Brand Associations**: Multiple brand relationships
- **Project Assignments**: Direct project access
- **Resource Allocation**: Hours available for different roles

---

## Project Management Features

### Project Structure

Projects in SGF Portal are organized with the following hierarchy:

```
Client
├── Brand(s)
    ├── Project(s) [Web/Print/Voyager/Publication/Video/Social Media]
        ├── Timeline (determines project type)
        ├── Phase(s)
            ├── Category(s)
                ├── Task(s)
                    ├── Comment(s)
                    └── Attachment(s)
```

### Project Types

Projects are categorized by timeline types:

1. **Web Project** (`type: 'web'`)

   - Standard website development projects
   - Follows all 5 phases: Define → Content/sitemap/wireframes → Design → Code → Deploy and Manage

2. **Print Project** (`type: 'print'`)

   - Print design and production projects
   - Modified phase structure for print workflows

3. **Voyager Project** (`type: 'voyager'`)

   - Long-term maintenance and support projects
   - Displayed separately in client portals
   - Ongoing management focus

4. **Publication Project** (`type: 'publication'`)

   - Publishing and editorial projects
   - Similar workflow to print projects

5. **Video Project** (`type: 'video'`)

   - Video production and editing projects
   - Specialized phase structure for video workflows

6. **Social Media Project** (`type: 'social'`)
   - Social media management and campaigns
   - Adapted phases for social media workflows

### Project Lifecycle

#### 1. Project Creation

- **Client Association**: Every project belongs to a client
- **Brand Relationship**: Projects are linked to specific brands
- **Job Code System**: Unique identifiers for project tracking
- **Timeline Assignment**: Projects have associated timelines
- **Resource Allocation**: Initial hour estimates for different roles

#### 2. Project Phases

Projects follow a structured phase system:

1. **Define Phase**

   - End-in-mind planning
   - Starting off the project right by deciding deliverables, parameters and metrics for success
   - Initial research and planning
   - Client requirements gathering
   - Project scope definition

2. **Content, sitemap, wireframes Phase**

   - Content organization and creation
   - Unifying your message across copy and visuals
   - Information architecture
   - Wireframe development

3. **Design Phase**

   - The message, visualized
   - Making sure your content shines through supporting imagery, visuals and branding
   - UI/UX design and prototyping
   - Design asset creation
   - Client design approval

4. **Code Phase**

   - HTML, CMS, etc.
   - Taking everything we've developed together and implementing it in a live environment
   - Code implementation
   - Feature development
   - Technical implementation

5. **Deploy and Manage Phase**
   - Launch, SEO, ongoing maintenance
   - Finishing off the project as strong as we started it
   - Production deployment
   - Go-live activities
   - Launch coordination
   - Ongoing maintenance and support

#### 3. Project Status Management

Projects have multiple status tracking mechanisms:

- **Primary Status**: Overall project status
- **Project Status**: Detailed project state
- **Phase Status**: Individual phase completion tracking
- **Soft Delete**: Projects can be archived without permanent deletion

### Project Data Model

```php
// Project Model Relationships
class Project extends Model
{
    use SoftDeletes;

    // Core project information
    protected $fillable = [
        'name', 'job_code', 'status', 'client_id',
        'timeline_id', 'harvest_link', 'invoice_schedule',
        'kickoff_message_id'
    ];

    // Relationships
    public function client() { return $this->belongsTo(Client::class); }
    public function timeline() { return $this->belongsTo(Timeline::class); }
    public function phases() {
        return $this->belongsToMany(Phase::class, 'project_phase')
                    ->withPivot('project_duration', 'project_target')
                    ->withTimestamps();
    }
    public function tasks() { return $this->hasMany(Task::class); }
    public function users() {
        return $this->belongsToMany(User::class, 'project_user');
    }
    public function brands() {
        return $this->belongsToMany(Brand::class, 'brand_projects');
    }
}
```

### Resource Allocation

#### Hour Tracking System

Projects track estimated hours for different roles:

- **PM Hours**: Project management time allocation
- **Designer Hours**: Design work estimates
- **Developer Hours**: Development time estimates
- **CS Hours**: Customer service/support hours

#### Resource Calculation

The system calculates resource utilization based on:

- **Available Hours**: From the resources table for different user roles
- **Allocated Hours**: Project hours assigned to team members
- **Utilization Percentage**: (allocated_hours / available_hours) \* 100
- **Overallocation**: Can exceed 100% when team members are overbooked

#### Weekly Resource Planning

- **32-hour Work Week**: Standard work week (6+ hours per day)
- **PM Allocation**: 20-30% of project manager time during active phases
- **Phase Transitions**: Resource reallocation when phases complete
- **Leave Accounting**: Employee leave time impacts calculations

### Project Messages & Communication

#### Message Threading System

Projects support structured communication through:

```sql
project_message_threads (
    id BIGINT PRIMARY KEY,
    project_id BIGINT FOREIGN KEY -> projects(id),
    subject VARCHAR(255),
    created_by BIGINT FOREIGN KEY -> users(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

project_messages (
    id BIGINT PRIMARY KEY,
    thread_id BIGINT FOREIGN KEY -> project_message_threads(id),
    user_id BIGINT FOREIGN KEY -> users(id),
    message_body TEXT,
    due_date TIMESTAMP NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

project_messages_attachments (
    id BIGINT PRIMARY KEY,
    message_id BIGINT FOREIGN KEY -> project_messages(id),
    file_name VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Kickoff Messages

Projects can have associated kickoff messages:

- **Kickoff Message ID**: Links to project_message_threads
- **Project Initialization**: Welcome messages and initial instructions
- **Client Onboarding**: Project introduction and expectations

---

## Task Management System

### Task Structure

Tasks are the fundamental work units in SGF Portal with the following characteristics:

#### Core Task Properties

```sql
tasks (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),                    -- Task title
    description TEXT NULLABLE,           -- Detailed task description
    project_id BIGINT,                   -- Associated project
    status_id BIGINT,                    -- Current task status
    due_date VARCHAR(255) NULLABLE,      -- Target completion date
    admin_id BIGINT,                     -- Task creator/admin
    attachemenet VARCHAR(255) NULLABLE,  -- File attachments
    deleted_at TIMESTAMP NULLABLE,       -- Soft delete timestamp
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

### Task Status System

#### Status Categories

The system uses a comprehensive status tracking system:

1. **Urgent**: High-priority tasks needing immediate action
2. **New**: Recently created tasks requiring attention
3. **In Progress**: Tasks currently being worked on
4. **Ready For Approval**: Tasks completed and awaiting approval
5. **Feedback**: Tasks requiring feedback or revisions
6. **Recently Finished**: Tasks completed within the last 30 days

#### Status Display Logic

- **Dashboard Display**: Only shows 'urgent' and 'new' status tasks for focused attention
- **Task Overview**: Displays all statuses with filtering options and counts
- **Project Tasks**: Shows tasks with status-specific icons (star icons for urgent tasks)
- **Recently Finished Filter**: Shows tasks completed within the last 30 days when specifically filtered
- **Status Icons**: Visual indicators for urgent tasks (star icons), other tasks display simply without icons
- **Task Completion**: When tasks are marked complete, they automatically get "Recently Finished" status

### Task Assignment System

#### User-Task Relationships

```sql
task_user (
    id BIGINT PRIMARY KEY,
    task_id BIGINT FOREIGN KEY -> tasks(id),
    user_id BIGINT FOREIGN KEY -> users(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Assignment Features

- **Multiple Assignees**: Tasks can be assigned to multiple users
- **Role-based Assignment**: Assignment based on user roles and capabilities
- **Workload Distribution**: Automatic assignment based on current workload
- **Notification System**: Assigned users receive notifications

### Task Categories & Phases

#### Category System

Tasks are organized by categories that correspond to project phases:

```sql
categories (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    order INTEGER,
    description TEXT NULLABLE,
    phase_id BIGINT FOREIGN KEY -> phases(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Phase Integration

- **Phase-Category Mapping**: Categories are linked to specific project phases
- **Workflow Organization**: Tasks flow through phases in structured order
- **Progress Tracking**: Phase completion based on task completion

### Task Comments & Collaboration

#### Real-time Comments System

```sql
comments (
    id BIGINT PRIMARY KEY,
    user_id BIGINT FOREIGN KEY -> users(id),
    task_id BIGINT FOREIGN KEY -> tasks(id),
    comment_parent_id BIGINT FOREIGN KEY -> comments(id),
    comment_body TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Comment Features

- **Nested Comments**: Support for comment replies
- **Real-time Updates**: Live updates using Laravel Reverb
- **User Mentions**: @ symbol mentions with dropdown suggestions
- **Rich Text Support**: Bold, italic, underline formatting
- **File Attachments**: Image and document uploads
- **Link Detection**: Automatic link highlighting and click-to-open

#### Comment Attachments

```sql
comment_attachments (
    id BIGINT PRIMARY KEY,
    comment_id BIGINT FOREIGN KEY -> comments(id),
    file_name VARCHAR(255),
    file_path VARCHAR(255),
    file_size INTEGER,
    mime_type VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

### Task Filtering & Search

#### TaskOverview Livewire Component

The main task management interface provides:

```php
class TaskOverview extends Component
{
    // Filter properties
    public $selectedStatus = 'all';
    public $sortField = 'created_at';
    public $sortDirection = 'asc';
    public $alphabet = null;
    public $search = '';
    public $year = 'all';
    public $selectedMonth = null;

    // Query string parameters for URL state
    protected $queryString = [
        'selectedStatus' => ['except' => 'all', 'as' => 'status'],
        'sortField' => ['except' => 'created_at'],
        'search' => ['except' => '', 'as' => 'job'],
        'alphabet' => ['except' => null],
        'year' => ['except' => null],
        'selectedMonth' => ['except' => null],
    ];
}
```

#### Filtering Capabilities

- **Status Filtering**: Filter by task status (all, new, urgent, completed, etc.)
- **Project Search**: Search by project name or job code
- **Alphabetical Filtering**: Filter projects by first letter
- **Date Filtering**: Filter by year and month
- **Recently Finished**: Special filter for recently completed tasks
- **Sorting Options**: Sort by various fields with direction control

#### Advanced Query Features

- **Soft Delete Awareness**: Filters out deleted tasks and projects
- **Permission-based Filtering**: Shows only tasks user has access to
- **Performance Optimization**: Efficient queries with proper indexing
- **Pagination**: Bootstrap-themed pagination for large datasets

## Calendar & Resource Allocation

### Calendar System Overview

The SGF Portal calendar system provides comprehensive scheduling and resource management capabilities with multiple data visualization layers.

#### Calendar Data Sources

The calendar displays information from multiple sources:

1. **Project Completion Dates** (displayed in blue)

   - Calculated from the highest phase ID target date
   - Automatically updated when phase dates change
   - Visible to all project stakeholders

2. **Phase Completion Dates** (color-coded by index)

   - Individual phase target dates from project_phase table
   - Color coding based on phase index for visual distinction
   - Clickable for detailed phase information

3. **Team Member Holidays and Leave**

   - Personal holidays with user name display (e.g., 'John is out')
   - Company-wide holidays
   - Leave time impacts resource calculations

4. **Resource Allocation Events**
   - Weekly resource utilization percentages
   - Team member availability and allocation
   - Project hour distribution

### Holiday Management System

#### Holiday Types

```sql
holidays (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    date DATE,
    type ENUM('personal', 'company', 'indian', 'us'),
    user_id BIGINT FOREIGN KEY -> users(id) NULLABLE,
    description TEXT NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Holiday Features

- **Admin Capabilities**: Admins can add holidays for all users
- **Personal Leave**: Individual vacation and sick days
- **Holiday Calendars**: Support for Indian and US holiday calendars
- **User-specific Display**: Personal holidays show user name
- **AJAX Integration**: Calendar events added without page refresh

#### Holiday Management Permissions

- **Super Admin**: Full holiday management for all users
- **Admin**: Holiday management within assigned teams/brands
- **Users**: Can request personal leave (approval workflow)
- **Clients**: View-only access to relevant holidays

### Resource Allocation System

#### Resource Tracking Architecture

The resource allocation system tracks weekly utilization across multiple dimensions:

```sql
resources (
    id BIGINT PRIMARY KEY,
    user_id BIGINT FOREIGN KEY -> users(id),
    role_type ENUM('developer', 'designer', 'pm', 'admin'),
    weekly_hours INTEGER,
    effective_date DATE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Utilization Calculation Logic

```php
// Resource utilization calculation
$utilization = ($allocated_hours / $available_hours) * 100;

// Can exceed 100% for overallocation scenarios
// Standard work week: 32 hours (6+ hours per day)
// PM allocation: 20-30% during design, code, deploy, manage phases
```

#### Resource Allocation Features

1. **Multi-role Support**

   - Users can have different hour allocations for different roles
   - Developer, designer, and PM roles tracked separately
   - No team separation - users can have multiple roles

2. **Weekly Planning**

   - Resource allocation displayed in weekly increments
   - Calendar extends beyond current month until project completion
   - Weekly transitions tracked when project phases complete

3. **Leave Integration**

   - Employee leave time reduces available hours
   - Holiday impact on resource calculations
   - Automatic adjustment for time off

4. **Project Hour Distribution**
   - Remaining project hours calculated by phase
   - Hours used vs. total allocated tracking
   - Different user roles have different hour commitments

#### Resource Allocation Display

The resource allocation calendar provides:

- **Calendar-based View**: Similar to main calendar layout
- **Weekly Events**: Resource percentages shown as calendar events
- **Format Display**: (allocated/available) hours format
- **Reduced Event Height**: Better visibility for multiple events
- **Holiday Integration**: Holidays displayed at end of resource percentages
- **Project Visibility**: Full project names (not truncated)
- **Clickable Details**: Project names link to complete project information

### Calendar Integration Features

#### Event Management

- **Clickable Events**: All calendar events provide detailed information
- **Modal Dialogs**: Event details displayed in overlay windows
- **AJAX Operations**: Add, edit, delete events without page refresh
- **Permission-based Actions**: Role-appropriate event management

#### Calendar Views

1. **Monthly View**: Standard month-by-month calendar display
2. **Resource View**: Focus on team utilization and allocation
3. **Project Timeline**: Project-centric view of deadlines and milestones
4. **Team Schedule**: Individual team member schedules and availability

#### Technical Implementation

- **Frontend**: Custom JavaScript calendar with Bootstrap styling
- **Backend**: Laravel controllers for event management
- **Real-time Updates**: Event changes broadcast to relevant users
- **Performance**: Efficient queries with proper caching

---

## Comments & Communication System

### Real-time Communication Architecture

SGF Portal implements a comprehensive real-time communication system using Laravel Reverb for WebSocket connections.

#### Core Communication Components

1. **Laravel Reverb Integration**

   - WebSocket server for real-time updates
   - Event broadcasting for live notifications
   - Scalable connection management

2. **Comment System**

   - Nested comment threads
   - Real-time comment updates
   - User mention system

3. **File Attachment System**
   - Image and document uploads
   - File preview capabilities
   - Attachment management

### Comment System Implementation

#### Comment Data Structure

```sql
comments (
    id BIGINT PRIMARY KEY,
    user_id BIGINT FOREIGN KEY -> users(id),
    task_id BIGINT FOREIGN KEY -> tasks(id),
    comment_parent_id BIGINT FOREIGN KEY -> comments(id),
    comment_body TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### Comment Features

1. **Rich Text Support**

   - Bold, italic, underline formatting
   - Keyboard shortcuts (Ctrl+B, Ctrl+I, Ctrl+U)
   - Line breaks with Shift+Enter
   - Link highlighting and click-to-open

2. **User Mentions**

   - @ symbol triggers user dropdown
   - Autocomplete user suggestions
   - Notification system for mentioned users
   - Proper mention replacement (e.g., '@per' becomes full tagged person)

3. **Nested Comments**
   - Reply-to-comment functionality
   - Threaded conversation display
   - Parent-child comment relationships

#### Comment Input System

The comment input box supports:

- **Multi-line Input**: Line breaks via Shift+Enter
- **Text Formatting**: Visual highlighting when formatting applied
- **Paste Support**: Images and formatted content handling
- **Content Sanitization**: Automatic cleanup of pasted content

### File Attachment System

#### Attachment Data Model

```sql
comment_attachments (
    id BIGINT PRIMARY KEY,
    comment_id BIGINT FOREIGN KEY -> comments(id),
    file_name VARCHAR(255),
    file_path VARCHAR(255),
    file_size INTEGER,
    mime_type VARCHAR(255),
    upload_user_id BIGINT FOREIGN KEY -> users(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

task_attachments (
    id BIGINT PRIMARY KEY,
    task_id BIGINT FOREIGN KEY -> tasks(id),
    file_name VARCHAR(255),
    file_path VARCHAR(255),
    file_size INTEGER,
    mime_type VARCHAR(255),
    upload_user_id BIGINT FOREIGN KEY -> users(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### File Management Features

1. **Upload Capabilities**

   - Drag-and-drop file upload
   - Multiple file selection
   - Progress indicators during upload
   - File type validation

2. **File Preview System**

   - Image preview with modal display
   - Document preview for supported formats
   - File download functionality
   - Thumbnail generation

3. **File Type Support**
   - Images: JPG, PNG, GIF, SVG, WebP
   - Documents: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
   - Archives: ZIP, RAR, 7Z
   - Code files: HTML, CSS, JS, PHP, PY
   - Text files: TXT, MD

#### File Icon System

```javascript
// File type to icon mapping
const iconMap = {
  pdf: "fa-file-pdf",
  doc: "fa-file-word",
  docx: "fa-file-word",
  xls: "fa-file-excel",
  xlsx: "fa-file-excel",
  jpg: "fa-file-image",
  png: "fa-file-image",
  zip: "fa-file-archive",
  rar: "fa-file-archive",
  html: "fa-file-code",
  css: "fa-file-code",
  default: "fa-file",
};
```

### Real-time Features Implementation

#### Event Broadcasting

```php
// Comment creation event
class CommentAdded implements ShouldBroadcast
{
    public $comment;
    public $task_id;

    public function broadcastOn()
    {
        return new Channel('task.' . $this->task_id);
    }

    public function broadcastWith()
    {
        return [
            'comment' => $this->comment,
            'user' => $this->comment->user,
            'timestamp' => $this->comment->created_at
        ];
    }
}
```

#### Frontend Real-time Integration

- **WebSocket Connections**: Automatic connection management
- **Event Listeners**: Real-time comment updates
- **UI Updates**: Dynamic comment insertion without page refresh
- **Notification System**: Toast notifications for new comments

### Notification System

#### Notification Types

1. **Comment Notifications**

   - New comments on assigned tasks
   - Replies to user comments
   - Mentions in comments

2. **Task Notifications**

   - Task assignment notifications
   - Task status changes
   - Due date reminders

3. **Project Notifications**
   - Project assignment
   - Phase completion
   - Milestone achievements

#### Notification Delivery

```php
// Mention notification
class MentionedInComment extends Notification
{
    public function via($notifiable)
    {
        return ['mail', 'database', 'broadcast'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('You were mentioned in a comment')
            ->line('You were mentioned in a comment on task: ' . $this->task->name)
            ->action('View Task', url('/tasks/' . $this->task->id));
    }
}
```

### Communication Best Practices

#### Content Management

- **Content Sanitization**: HTML content cleaned for security
- **Link Processing**: Automatic link detection and formatting
- **Image Optimization**: Automatic image resizing and compression
- **Spam Prevention**: Rate limiting and content validation

#### User Experience

- **Timezone Handling**: Timestamps displayed in user's timezone
- **Mobile Responsiveness**: Touch-friendly interface for mobile devices
- **Accessibility**: Screen reader support and keyboard navigation
- **Performance**: Lazy loading for large comment threads

## API Documentation

### API Architecture

SGF Portal provides a comprehensive RESTful API built on Laravel's routing system with Sanctum authentication.

#### Authentication System

```php
// API Authentication using Laravel Sanctum
Route::middleware('auth:sanctum')->group(function () {
    // Protected API routes
});

// Login endpoint
POST /login
{
    "email": "<EMAIL>",
    "password": "password"
}



#### API Route Structure

Based on the routes/web.php analysis, the system includes these main controllers:

- **AuthController**: Authentication and user management
- **ProjectController**: Project CRUD operations
- **TaskController**: Task management
- **DashboardController**: Dashboard data and statistics
- **AdminDashboardController**: Administrative functions
- **ClientController**: Client management
- **BrandController**: Brand operations
- **TeamController**: Team member management
- **HolidayController**: Holiday and leave management
- **EssentialMetricsController**: Metrics and reporting
- **FileUploadController**: File management
- **ProjectMessageController**: Project communication

### Core API Endpoints

#### Authentication Endpoints

```http
POST /login
POST /logout



```

#### Project Management API

```http
GET    /projects
POST   /projects
GET    /projects/{id}

DELETE /projects/{id}
GET    /projects/{id}/tasks
GET    /projects/{id}/phases
GET    /projects/{id}/messages
POST   /projects/{id}/messages
```

#### Task Management API

```http
GET    /tasks
POST   /tasks
GET    /tasks/{id}
PUT    /tasks/{id}
DELETE /tasks/{id}
POST   /tasks/{id}/comments
GET    /tasks/{id}/comments
POST   /tasks/{id}/attachments
GET    /tasks/{id}/attachments
```

#### User Management API

```http
GET    /users
POST   /users
GET    /users/{id}
PUT    /users/{id}
DELETE /users/{id}
GET    /users/{id}/projects
GET    /users/{id}/tasks
```

#### Client & Brand API

```http
GET    /clients
POST   /clients
GET    /clients/{id}
PUT    /clients/{id}
DELETE /clients/{id}

GET    /brands
POST   /brands
GET    /brands/{id}
PUT    /brands/{id}
DELETE /brands/{id}
```

### API Request/Response Formats


#### Task Creation Request

```json
POST /tasks
{
    "name": "Design Homepage Layout",
    "description": "Create wireframes and mockups for the homepage",
    "project_id": 1,
    "status_id": 1,
    "due_date": "2025-02-01",
    "assigned_users": [2, 3],
    "category_id": 2,
    "phase_id": 2
}
```

#### Task Response

```json
{
  "id": 1,
  "name": "Design Homepage Layout",
  "description": "Create wireframes and mockups for the homepage",
  "project": {
    "id": 1,
    "name": "New Website Project",
    "job_code": "WEB-001-25"
  },
  "status": {
    "id": 1,
    "name": "New"
  },
  "assigned_users": [
    {
      "id": 2,
      "name": "Jane Designer",
      "role": "designer"
    }
  ],
  "comments_count": 3,
  "attachments_count": 1,
  "due_date": "2025-02-01",
  "created_at": "2025-01-15T10:00:00Z",
  "updated_at": "2025-01-15T10:00:00Z"
}
```

### API Error Handling

#### Standard Error Response Format

```json
{
  "error": true,
  "message": "Validation failed",
  "errors": {
    "name": ["The name field is required."],
    "email": ["The email field must be a valid email address."]
  },
  "status_code": 422
}
```

#### HTTP Status Codes

- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **422**: Validation Error
- **500**: Internal Server Error

### API Rate Limiting

```php
// Rate limiting configuration
Route::middleware(['throttle:api'])->group(function () {
    // API routes with rate limiting
});

// Custom rate limits for different endpoints
Route::middleware(['throttle:60,1'])->group(function () {
    // 60 requests per minute
});
```

---

## Frontend Components

### Livewire Components Architecture

SGF Portal uses Livewire 3.6 for interactive frontend components, providing real-time updates without full page refreshes.

#### Core Livewire Components

1. **TaskOverview Component**

   - Main task management interface
   - Advanced filtering and sorting
   - Real-time task updates
   - Pagination and search

2. **Admin Components**

   - ClientList: Client management interface
   - ProjectList: Project administration
   - UserManagement: Team member administration

3. **Calendar Components**
   - ResourceAllocation: Weekly resource planning
   - HolidayManagement: Holiday and leave management
   - ProjectTimeline: Project scheduling

### TaskOverview Component Deep Dive

#### Component Structure

```php
class TaskOverview extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';

    // Filter properties
    public $selectedStatus = 'all';
    public $sortField = 'created_at';
    public $sortDirection = 'asc';
    public $showSortDropdown = false;
    public $alphabet = null;
    public $search = '';
    public $year = 'all';
    public $selectedMonth = null;
    public $billingMonths = [];
    public $years = [];

    // URL state management
    protected $queryString = [
        'selectedStatus' => ['except' => 'all', 'as' => 'status'],
        'sortField' => ['except' => 'created_at'],
        'search' => ['except' => '', 'as' => 'job'],
        'alphabet' => ['except' => null],
        'year' => ['except' => null],
        'selectedMonth' => ['except' => null],
    ];
}
```

#### Advanced Filtering Logic

```php
// Complex query building with multiple filters
public function getTasksByMonth($year, $month)
{
    $query = Task::query()
        ->whereNull('tasks.deleted_at')
        ->whereHas('project', function ($q) {
            $q->whereNull('deleted_at');

            // Search filter
            if (!empty($this->search)) {
                $q->where(function ($subQ) {
                    $subQ->where('name', 'like', '%' . $this->search . '%')
                         ->orWhere('job_code', 'like', '%' . $this->search . '%');
                });
            }

            // Alphabetical filter
            if (!empty($this->alphabet)) {
                $q->whereRaw('LEFT(UPPER(name), 1) = ?', [strtoupper($this->alphabet)]);
            }
        });

    // Date filtering
    if ($this->selectedMonth) {
        $query->whereYear('created_at', $year)
              ->whereMonth('created_at', $month);
    }

    return $query->with(['project', 'status', 'users'])->get();
}
```

#### Performance Optimizations

- **Lazy Loading**: Components load data on demand
- **Query Optimization**: Efficient database queries with proper relationships
- **Caching**: Strategic caching of frequently accessed data
- **Pagination**: Bootstrap-themed pagination for large datasets
- **Debounced Search**: Search input debouncing to reduce server requests

### JavaScript Integration

#### Custom JavaScript Components

1. **Calendar System**

   - Interactive calendar with drag-and-drop
   - Event management and editing
   - Real-time updates via WebSocket

2. **File Upload System**

   - Drag-and-drop file uploads
   - Progress indicators
   - File type validation

3. **Rich Text Editor Integration**
   - TinyMCE configuration
   - Custom toolbar setup
   - Image upload handling

#### TinyMCE Configuration

```javascript
tinymce.init({
  selector:
    "textarea.project_message_description, textarea.current_step_description",
  height: 200,
  plugins: [
    "lists",
    "advlist",
    "autolink",
    "link",
    "image",
    "charmap",
    "preview",
    "anchor",
    "searchreplace",
    "wordcount",
    "code",
    "fullscreen",
    "insertdatetime",
    "table",
  ],
  toolbar:
    "undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen",
  menubar: false,
  content_style: `
        body {
            font-family: Helvetica, Arial, sans-serif;
            font-size: 16px;
            background-color: #1e1e1e;
            color: #f5f5f5;
        }
        p { margin: 0; padding: 0; }
    `,
  setup: function (editor) {
    // Custom setup for dark theme
    editor.on("init", function () {
      editor.getDoc().body.style.backgroundColor = "#1e1e1e";
    });
  },
});
```

### CSS Architecture

#### SCSS Structure

The frontend uses a modular SCSS architecture:

```scss
// Main application styles
@import "fonts";
@import "mixins";
@import "header";
@import "navigation";
@import "clientPortal";
@import "projectDashboard";
@import "formFields";
@import "projectsHub";
@import "uploadedFiles";
@import "teamDashboard";
@import "plan";
@import "footer";
@import "typography";
@import "cursor";
@import "common";
@import "loader";
```

#### Design System Variables

```scss
// Color system
$white: #fff;
$black: #000;
$siteBlack: #111;
$orange: #ff4c00;
$green: #65ccb0;
$pink: #f45689;
$purple: #a15cd4;
$lightFont: #cbcbcb;
$darkFont: #323232;
$hr: #888;

// Typography
$inter: "Inter", sans-serif;
$play: "Playfair Display", serif;
$futura: "Futura Std", serif;
```

#### Responsive Design

```scss
// Responsive breakpoints
@media (min-width: 992px) {
  .header .header_float {
    height: 120px;
    padding-bottom: 30px;
    padding-top: 30px;
  }
}

@media (max-width: 991px) {
  .header .brand img {
    height: 45px;
    width: 115px;
  }
}
```

### Component Interaction Patterns

#### Real-time Updates

```javascript
// Livewire event listeners
document.addEventListener("livewire:load", function () {
  // Component loaded
  initializeInteractiveElements();
});

document.addEventListener("livewire:update", function () {
  // Component updated
  refreshInteractiveElements();
});

// Custom event handling
Livewire.on("taskUpdated", function (taskId) {
  // Handle task update
  updateTaskDisplay(taskId);
  showNotification("Task updated successfully");
});
```

#### Form Handling

```php
// Livewire form validation
public function rules()
{
    return [
        'selectedStatus' => 'string|in:all,new,urgent,completed,cancelled',
        'search' => 'string|max:255',
        'sortField' => 'string|in:created_at,name,due_date,status',
        'sortDirection' => 'string|in:asc,desc'
    ];
}

public function updated($propertyName)
{
    $this->validateOnly($propertyName);
    $this->resetPage(); // Reset pagination on filter change
}
```

## Installation & Setup

### System Requirements

#### Server Requirements

- **PHP**: 8.2 or higher
- **Database**: MySQL 8.0+ or MariaDB 10.3+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Memory**: Minimum 512MB RAM (2GB+ recommended)
- **Storage**: Minimum 1GB free space

#### PHP Extensions

Required PHP extensions:

```bash
# Core extensions
php-bcmath
php-ctype
php-curl
php-dom
php-fileinfo
php-json
php-mbstring
php-openssl
php-pcre
php-pdo
php-tokenizer
php-xml

# Database extensions
php-mysql
php-pdo_mysql

# Optional but recommended
php-gd
php-imagick
php-zip
php-intl
```

#### Node.js Requirements

- **Node.js**: 18.0+ or 20.0+
- **NPM**: 9.0+ or Yarn 1.22+

### Installation Process

#### 1. Clone Repository

```bash
git clone https://github.com/your-repo/sgf-portal.git
cd sgf-portal
```

#### 2. Install PHP Dependencies

```bash
composer install --optimize-autoloader --no-dev
```

For development environment:

```bash
composer install
```

#### 3. Install Node.js Dependencies

```bash
npm install
```

Or using Yarn:

```bash
yarn install
```

#### 4. Environment Configuration

```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

#### 5. Database Setup

```bash
# Create database
mysql -u root -p
CREATE DATABASE sgf_portal CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;

# Run migrations
php artisan migrate

# Seed initial data
php artisan db:seed
```

#### 6. Storage Setup

```bash
# Create storage link
php artisan storage:link

# Set permissions
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

#### 7. Build Assets

For production:

```bash
npm run build
```

For development:

```bash
npm run dev
```

#### 8. Queue Setup (Optional)

```bash
# Install supervisor for queue management
sudo apt-get install supervisor

# Create supervisor configuration
sudo nano /etc/supervisor/conf.d/sgf-portal-worker.conf
```

Supervisor configuration:

```ini
[program:sgf-portal-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/sgf-portal/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/sgf-portal/storage/logs/worker.log
stopwaitsecs=3600
```

### Development Environment Setup

#### Using Laravel Sail (Docker)

```bash
# Install Sail
composer require laravel/sail --dev

# Publish Sail configuration
php artisan sail:install

# Start development environment
./vendor/bin/sail up -d

# Run migrations in Sail
./vendor/bin/sail artisan migrate

# Build assets in Sail
./vendor/bin/sail npm run dev
```

#### Local Development Server

```bash
# Start Laravel development server
php artisan serve

# Start Vite development server (separate terminal)
npm run dev

# Start queue worker (separate terminal)
php artisan queue:work

# Start Reverb WebSocket server (separate terminal)
php artisan reverb:start
```

#### Development Tools Setup

```bash
# Install development dependencies
composer install
npm install

# Set up code quality tools
./vendor/bin/pint --test
./vendor/bin/phpstan analyse

# Run tests
php artisan test
```

### Production Deployment

#### Web Server Configuration

##### Apache Configuration

```apache
<VirtualHost *:80>
    ServerName sgf-portal.example.com
    DocumentRoot /var/www/sgf-portal/public

    <Directory /var/www/sgf-portal/public>
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/sgf-portal_error.log
    CustomLog ${APACHE_LOG_DIR}/sgf-portal_access.log combined
</VirtualHost>
```

##### Nginx Configuration

```nginx
server {
    listen 80;
    server_name sgf-portal.example.com;
    root /var/www/sgf-portal/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

#### Production Optimization

```bash
# Optimize autoloader
composer install --optimize-autoloader --no-dev

# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Build production assets
npm run build

# Set proper permissions
chown -R www-data:www-data /var/www/sgf-portal
chmod -R 755 /var/www/sgf-portal
chmod -R 775 /var/www/sgf-portal/storage
chmod -R 775 /var/www/sgf-portal/bootstrap/cache
```

---

## Configuration

### Environment Variables

#### Core Application Settings

```env
# Application
APP_NAME="SGF Portal"
APP_ENV=production
APP_KEY=base64:your-generated-key
APP_DEBUG=false
APP_TIMEZONE=UTC
APP_URL=https://sgf-portal.example.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sgf_portal
DB_USERNAME=sgf_user
DB_PASSWORD=secure_password

# Cache
CACHE_STORE=redis
CACHE_PREFIX=sgf_portal

# Session
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Queue
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database-uuids
```

#### Email Configuration

```env
# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

#### Broadcasting & WebSocket

```env
# Broadcasting
BROADCAST_CONNECTION=reverb

# Reverb
REVERB_APP_ID=your-app-id
REVERB_APP_KEY=your-app-key
REVERB_APP_SECRET=your-app-secret
REVERB_HOST="localhost"
REVERB_PORT=8080
REVERB_SCHEME=http

# Pusher (if using Pusher instead of Reverb)
PUSHER_APP_ID=your-pusher-app-id
PUSHER_APP_KEY=your-pusher-key
PUSHER_APP_SECRET=your-pusher-secret
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1
```

#### File Storage

```env
# Filesystem
FILESYSTEM_DISK=local

# AWS S3 (if using cloud storage)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=sgf-portal-files
AWS_USE_PATH_STYLE_ENDPOINT=false
```

#### Redis Configuration

```env
# Redis
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_PREFIX=sgf_portal_database_
```

### Database Configuration

#### MySQL Optimization

```sql
-- MySQL configuration for optimal performance
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_type = 1
query_cache_size = 256M
max_connections = 200
thread_cache_size = 16
table_open_cache = 4000
```

#### Database Indexes

Key indexes for performance:

```sql
-- Users table indexes
CREATE INDEX idx_users_role_id ON users(role_id);
CREATE INDEX idx_users_email ON users(email);

-- Projects table indexes
CREATE INDEX idx_projects_client_id ON projects(client_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_deleted_at ON projects(deleted_at);

-- Tasks table indexes
CREATE INDEX idx_tasks_project_id ON tasks(project_id);
CREATE INDEX idx_tasks_status_id ON tasks(status_id);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_tasks_deleted_at ON tasks(deleted_at);

-- Comments table indexes
CREATE INDEX idx_comments_task_id ON comments(task_id);
CREATE INDEX idx_comments_user_id ON comments(user_id);
CREATE INDEX idx_comments_created_at ON comments(created_at);
```

### Application Configuration

#### Logging Configuration

```php
// config/logging.php
'channels' => [
    'stack' => [
        'driver' => 'stack',
        'channels' => ['single', 'slack'],
        'ignore_exceptions' => false,
    ],

    'single' => [
        'driver' => 'single',
        'path' => storage_path('logs/laravel.log'),
        'level' => env('LOG_LEVEL', 'debug'),
    ],

    'daily' => [
        'driver' => 'daily',
        'path' => storage_path('logs/laravel.log'),
        'level' => env('LOG_LEVEL', 'debug'),
        'days' => 14,
    ],

    'slack' => [
        'driver' => 'slack',
        'url' => env('LOG_SLACK_WEBHOOK_URL'),
        'username' => 'SGF Portal',
        'emoji' => ':boom:',
        'level' => env('LOG_LEVEL', 'critical'),
    ],
],
```

#### Queue Configuration

```php
// config/queue.php
'connections' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => env('REDIS_QUEUE_CONNECTION', 'default'),
        'queue' => env('REDIS_QUEUE', 'default'),
        'retry_after' => 90,
        'block_for' => null,
        'after_commit' => false,
    ],
],

'batching' => [
    'database' => env('DB_CONNECTION', 'mysql'),
    'table' => 'job_batches',
],

'failed' => [
    'driver' => env('QUEUE_FAILED_DRIVER', 'database-uuids'),
    'database' => env('DB_CONNECTION', 'mysql'),
    'table' => 'failed_jobs',
],
```

## Troubleshooting

### Common Issues & Solutions

#### Database Issues

##### MySQL ONLY_FULL_GROUP_BY Error

**Problem**: Error when using GROUP BY with MySQL's ONLY_FULL_GROUP_BY SQL mode enabled.

**Solution**: Include all non-aggregated columns in the GROUP BY clause:

```sql
-- Incorrect query
SELECT project_id, COUNT(*) as task_count, name
FROM tasks
GROUP BY project_id;

-- Correct query
SELECT project_id, COUNT(*) as task_count, name
FROM tasks
GROUP BY project_id, name;
```

**Laravel Query Builder Solution**:

```php
// Use selectRaw with proper grouping
$tasks = Task::selectRaw('project_id, COUNT(*) as task_count, MAX(name) as name')
    ->groupBy('project_id')
    ->get();
```

##### Database Connection Issues

**Problem**: "SQLSTATE[HY000] [2002] Connection refused"

**Solutions**:

1. Check database service status:

```bash
sudo systemctl status mysql
sudo systemctl start mysql
```

2. Verify database credentials in `.env`:

```env
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sgf_portal
DB_USERNAME=correct_username
DB_PASSWORD=correct_password
```

3. Test database connection:

```bash
php artisan tinker
DB::connection()->getPdo();
```

##### Migration Issues

**Problem**: Migration fails or tables not created properly.

**Solutions**:

1. Reset migrations:

```bash
php artisan migrate:reset
php artisan migrate
```

2. Fresh migration with seeding:

```bash
php artisan migrate:fresh --seed
```

3. Check migration status:

```bash
php artisan migrate:status
```

#### Comment System Issues

##### Extra @ Symbol on Line Wrap

**Problem**: When user mentions wrap to the next line, an extra @ symbol remains on the previous line.

**Solution**: Implement proper mention replacement logic:

```javascript
// Fix mention replacement when text wraps
function replaceMention(text, selectedUser) {
  // Find the last @ symbol and replace the partial mention
  const lastAtIndex = text.lastIndexOf("@");
  if (lastAtIndex !== -1) {
    const beforeAt = text.substring(0, lastAtIndex);
    const afterMention = text.substring(text.indexOf(" ", lastAtIndex));
    return beforeAt + "@" + selectedUser.name + afterMention;
  }
  return text;
}
```

##### Incorrect Timezone Display

**Problem**: Message timestamps show incorrect time for users in different timezones.

**Solution**: Implement proper timezone handling:

```php
// In your Comment model
public function getCreatedAtAttribute($value)
{
    return Carbon::parse($value)
        ->setTimezone(auth()->user()->timezone ?? config('app.timezone'))
        ->diffForHumans();
}
```

```javascript
// Frontend timezone handling
function formatTimestamp(timestamp, userTimezone) {
  return new Date(timestamp).toLocaleString("en-US", {
    timeZone: userTimezone,
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}
```

##### Image Paste Not Working

**Problem**: Pasting images into comment input doesn't work properly.

**Solution**: Implement proper paste event handling:

```javascript
document.addEventListener("paste", function (e) {
  const items = e.clipboardData.items;

  for (let i = 0; i < items.length; i++) {
    if (items[i].type.indexOf("image") !== -1) {
      e.preventDefault();

      const blob = items[i].getAsFile();
      const formData = new FormData();
      formData.append("image", blob);

      // Upload image via AJAX
      uploadImage(formData);
    }
  }
});
```

#### Performance Issues

##### Slow Page Load Times

**Problem**: Pages load slowly, especially with large datasets.

**Solutions**:

1. **Enable Query Caching**:

```php
// In your controller
$tasks = Cache::remember('user_tasks_' . auth()->id(), 300, function () {
    return Task::with(['project', 'users', 'status'])
        ->where('assigned_to', auth()->id())
        ->get();
});
```

2. **Optimize Database Queries**:

```php
// Use eager loading to prevent N+1 queries
$projects = Project::with(['tasks.users', 'client', 'phases'])
    ->get();

// Use select to limit columns
$tasks = Task::select('id', 'name', 'project_id', 'status_id')
    ->with(['project:id,name', 'status:id,name'])
    ->get();
```

3. **Implement Pagination**:

```php
// In Livewire component
public function render()
{
    return view('livewire.task-overview', [
        'tasks' => $this->getTasksQuery()->paginate(20)
    ]);
}
```

##### High Memory Usage

**Problem**: Application consumes excessive memory.

**Solutions**:

1. **Use Chunking for Large Datasets**:

```php
// Process large datasets in chunks
Task::chunk(100, function ($tasks) {
    foreach ($tasks as $task) {
        // Process task
    }
});
```

2. **Clear Unnecessary Data**:

```php
// Clear model relationships when not needed
$tasks = Task::all();
$tasks->each->unsetRelations();
```

3. **Optimize Composer Autoloader**:

```bash
composer dump-autoload --optimize --classmap-authoritative
```

#### File # SGF Portal - System Architecture & Documentation

## Table of Contents

1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [Database Structure](#database-structure)
4. [Core Features](#core-features)
5. [Installation & Setup](#installation--setup)
6. [Configuration](#configuration)
7. [User Management](#user-management)
8. [Project Management](#project-management)
9. [Task Management](#task-management)
10. [Real-time Features](#real-time-features)
11. [File Management](#file-management)
12. [API Endpoints](#api-endpoints)
13. [Troubleshooting](#troubleshooting)
14. [Performance Optimization](#performance-optimization)
15. [Security](#security)
16. [Deployment](#deployment)

---

## System Overview

**SGF Portal** is a comprehensive project management system built with Laravel 12, designed for managing projects, tasks, teams, and client communications with real-time collaboration features.

### Technology Stack

- **Backend**: Laravel 12 (PHP 8.2+)
- **Database**: MySQL
- **Real-time**: Laravel Reverb (WebSocket)
- **Frontend**: Livewire 3.6, Alpine.js, Bootstrap
- **Authentication**: Laravel Sanctum
- **Permissions**: Spatie Laravel Permission
- **File Storage**: Laravel Filesystem
- **Queue**: Database/Redis
- **Caching**: Database/Redis

### Key Dependencies

```json
{
  "laravel/framework": "^12.0",
  "livewire/livewire": "^3.6",
  "laravel/reverb": "^1.5",
  "spatie/laravel-permission": "^6.16",
  "spatie/laravel-pdf": "^1.5"
}
```

---

## Architecture

### MVC Structure

```
app/
├── Console/Commands/          # Artisan commands
├── Events/                    # Event classes
│   └── CommentAdded.php      # Real-time comment broadcasting
├── Http/
│   ├── Controllers/          # Request handling
│   │   ├── AuthController.php
│   │   ├── DashboardController.php
│   │   ├── ProjectController.php
│   │   ├── TaskController.php
│   │   └── ...
│   └── Middleware/           # Custom middleware
├── Livewire/                 # Interactive components
│   ├── TaskOverview.php     # Main task management
│   ├── ProjectTasks.php     # Project-specific tasks
│   └── Admin/               # Admin components
├── Models/                   # Eloquent models
│   ├── User.php
│   ├── Project.php
│   ├── Task.php
│   ├── Client.php
│   └── ...
├── Notifications/            # Email/push notifications
└── Providers/               # Service providers
```

### Database Architecture

#### Core Tables

1. **users** - User accounts and authentication
2. **projects** - Project management
3. **tasks** - Task tracking and assignment
4. **clients** - Client information
5. **brands** - Brand management
6. **roles** - User roles (SuperAdmin, Admin, Team Member, Client)
7. **permissions** - Granular permissions
8. **comments** - Task comments with threading
9. **project_messages** - Project communication
10. **files** - File attachments and storage

#### Relationship Structure

```
Users ──┬── Projects (many-to-many)
        ├── Tasks (many-to-many)
        ├── Comments (one-to-many)
        ├── Roles (many-to-many)
        └── Clients (many-to-many)

Projects ──┬── Tasks (one-to-many)
           ├── Clients (many-to-one)
           ├── Phases (many-to-many)
           ├── Messages (one-to-many)
           └── Files (one-to-many)

Tasks ──┬── Comments (one-to-many)
        ├── Attachments (one-to-many)
        ├── Users (many-to-many)
        └── Status (many-to-one)
```

---

## Database Structure

### Key Tables Schema

#### Projects Table
```sql
CREATE TABLE projects (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    job_code VARCHAR(255),
    status_id BIGINT,
    client_id BIGINT,
    assigned_by BIGINT,
    timeline_id BIGINT,
    category_id BIGINT,
    phase_id BIGINT,
    harvest_link TEXT,
    invoice_schedule VARCHAR(255),
    kickoff_type ENUM('yes', 'no') DEFAULT 'no',
    kickoff_title VARCHAR(255),
    kickoff_description TEXT,
    kickoff_file VARCHAR(255),
    archived BOOLEAN DEFAULT 0,
    estimated_hours INT,
    cs_hours DECIMAL(8,2),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

#### Tasks Table
```sql
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    project_id BIGINT,
    status_id BIGINT,
    due_date VARCHAR(255),
    admin_id BIGINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

#### Users Table
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role_id BIGINT,
    access_level_id BIGINT,
    email_verified_at TIMESTAMP NULL,
    remember_token VARCHAR(100),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

---

## Core Features

### 1. Project Management

#### Project Lifecycle
- **Creation**: Job code assignment, client linking, phase setup
- **Tracking**: Status updates, progress monitoring, timeline management
- **Collaboration**: Team assignment, message center, file sharing
- **Completion**: Phase completion tracking, archival system

#### Project Phases
1. **Define** - Project planning and requirements
2. **Design** - UI/UX design and mockups
3. **Code** - Development and implementation
4. **Deploy** - Testing and deployment
5. **Billing** - Invoice and payment processing

### 2. Task Management System

#### Task Statuses
- **New** - Recently created tasks
- **Urgent** - High-priority tasks
- **In Progress** - Active development
- **Recently Finished** - Completed within 30 days
- **Past Finished** - Completed over 30 days ago

#### Task Features
- Real-time status updates
- Multi-user assignment
- Due date tracking
- Comment threading
- File attachments
- Progress tracking

### 3. User Role System

#### Role Hierarchy
1. **SuperAdmin** - Full system access
2. **Admin** - Project and user management
3. **Team Member** - Task execution and collaboration
4. **Client Member** - Project viewing and communication

#### Permission Matrix
```php
// Example permissions
'projects-view', 'projects-create', 'projects-edit', 'projects-delete'
'tasks-view', 'tasks-create', 'tasks-edit', 'tasks-delete'
'users-view', 'users-create', 'users-edit', 'users-delete'
'clients-view', 'clients-create', 'clients-edit', 'clients-delete'
'brands-view', 'brands-create', 'brands-edit', 'brands-delete'
```

---

## Installation & Setup

### System Requirements

- PHP 8.2 or higher
- MySQL 8.0 or higher
- Composer 2.0+
- Node.js 18+ (for asset compilation)
- Redis (optional, for caching/queues)

### Installation Steps

1. **Clone Repository**:
```bash
git clone <repository-url> sgf-portal
cd sgf-portal
```

2. **Install Dependencies**:
```bash
composer install
npm install
```

3. **Environment Setup**:
```bash
cp .env.example .env
php artisan key:generate
```

4. **Database Configuration**:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sgf-portal
DB_USERNAME=root
DB_PASSWORD=
```

5. **Run Migrations**:
```bash
php artisan migrate
php artisan db:seed
```

6. **Storage Setup**:
```bash
php artisan storage:link
chmod -R 775 storage bootstrap/cache
```

7. **Asset Compilation**:
```bash
npm run build
```

---

## Configuration

### Environment Variables

#### Application Settings
```env
APP_NAME="ShieldsSGF Portal"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com
```

#### Database Configuration
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sgf_portal
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

#### Real-time Configuration (Laravel Reverb)
```env
REVERB_APP_ID=298112
REVERB_APP_KEY=your-app-key
REVERB_APP_SECRET=your-app-secret
REVERB_HOST="localhost"
REVERB_PORT=8080
REVERB_SCHEME=http

VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="${REVERB_PORT}"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"
```

#### Mail Configuration
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.ethereal.email
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
```

#### Queue Configuration
```env
QUEUE_CONNECTION=database
CACHE_STORE=database
BROADCAST_CONNECTION=reverb
```

---

## User Management

### Authentication Flow

1. **Login Process**:
   - Email/password validation
   - Role-based dashboard redirection
   - Session management

2. **Role Assignment**:
```php
// Assign role to user
$user = User::find(1);
$user->role_id = Role::where('name', 'Admin')->first()->id;
$user->save();
```

3. **Permission Checking**:
```php
// In controllers/middleware
if (!auth()->user()->hasRole(['Admin', 'SuperAdmin'])) {
    abort(403, 'Unauthorized');
}
```

### User Dashboard Routing

```php
// DashboardController@index
if (Auth::user()->role->name == 'SuperAdmin') {
    return redirect()->route('admin-dashboard');
} elseif (Auth::user()->role->name == 'Admin') {
    return redirect()->route('admin-dashboard');
} elseif (Auth::user()->role->name == 'Client Member') {
    return redirect()->route('client.dashboard');
} else {
    // Team member dashboard
    return view('dashboard', compact('groupedTasks', 'projects'));
}
```

---

## Project Management

### Project Creation Workflow

1. **Basic Information**:
   - Project name and job code
   - Client assignment
   - Timeline and phases

2. **Team Assignment**:
```php
// Assign users to project
$project->users()->attach($userIds);
```

3. **Phase Management**:
```php
// Project phases with pivot data
$project->phases()->attach($phaseId, [
    'project_duration' => 30,
    'project_target' => 'Complete design phase'
]);
```

### Project Status Tracking

#### Status Hub Features
- Real-time project progress
- Phase completion tracking
- Resource allocation
- Timeline visualization

#### Project Archival
```php
// Archive project
$project->update(['archived' => 1]);

// Unarchive project
$project->update(['archived' => 0]);
```

---

## Task Management

### Task Lifecycle

1. **Creation**:
```php
$task = Task::create([
    'name' => $request->task_name,
    'description' => $request->task_description,
    'project_id' => $request->project_id,
    'status_id' => $request->task_status,
    'due_date' => $request->due_date
]);

// Assign users
$task->users()->attach($request->task_assigned_to);
```

2. **Status Updates**:
```php
// Update task status
$task->status_id = $newStatusId;
$task->save();
```

3. **Comment System**:
```php
$comment = Comments::create([
    'user_id' => Auth::id(),
    'task_id' => $taskId,
    'comment_body' => $request->input('comment-body'),
    'comment_parent_id' => $parentId // For threading
]);
```

### Livewire Task Overview Component

#### Key Features
- Real-time filtering by status
- Alphabet-based project filtering
- Year and month-based filtering
- Pagination with load more
- Search functionality

#### Component Properties
```php
class TaskOverview extends Component
{
    public $selectedStatus = 'all';
    public $sortField = 'created_at';
    public $sortDirection = 'asc';
    public $search = '';
    public $year = 'all';
    public $selectedMonth = null;
    public $alphabet = null;
}
```

---

## Real-time Features

### Laravel Reverb Setup

1. **Server Configuration**:
```bash
# Start Reverb server
php artisan reverb:start --debug
```

2. **Event Broadcasting**:
```php
// CommentAdded Event
class CommentAdded implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function broadcastOn()
    {
        return new Channel('task.' . $this->task_id);
    }

    public function broadcastWith()
    {
        return [
            'comment' => $this->comment,
            'user' => $this->comment->user,
            'task_id' => $this->task_id
        ];
    }
}
```

3. **Frontend Integration**:
```javascript
// Echo configuration
window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY,
    wsHost: import.meta.env.VITE_REVERB_HOST,
    wsPort: import.meta.env.VITE_REVERB_PORT,
    wssPort: import.meta.env.VITE_REVERB_PORT,
    forceTLS: false,
    enabledTransports: ['ws', 'wss'],
});

// Listen for comment updates
Echo.channel('task.' + taskId)
    .listen('CommentAdded', (e) => {
        // Update UI with new comment
    });
```

### Notification System

#### Email Notifications
```php
// Task assignment notification
class TaskAssigned extends Notification
{
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('New Task Assigned')
            ->line('You have been assigned a new task.')
            ->action('View Task', route('view-task', $this->task->id));
    }
}
```

---

## File Management

### File Upload System

1. **Configuration**:
```php
// config/filesystems.php
'local' => [
    'driver' => 'local',
    'root' => storage_path('app'),
    'permissions' => [
        'file' => ['public' => 0644, 'private' => 0600],
        'dir' => ['public' => 0755, 'private' => 0700],
    ],
],
```

2. **Upload Handling**:
```php
// File upload in comments
if ($request->hasFile('comment_attachments')) {
    $attachments = $request->file('comment_attachments');
    $uploadPath = 'public/uploads';

    foreach ($attachments as $index => $file) {
        $filename = time() . '(' . ($index + 1) . ')' . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs('uploads', $filename, 'public');
        
        $comment->commentAttachments()->create([
            'comment_attachment' => $filename,
        ]);
    }
}
```

3. **File Download**:
```php
// FileController@download
public function download(File $file)
{
    $path = storage_path('app/public/' . $file->file_path);
    
    if (!file_exists($path)) {
        abort(404);
    }
    
    return response()->download($path, $file->original_name);
}
```

---

## API Endpoints

### Authentication Routes
```php
// Guest routes
Route::middleware(['guest'])->group(function () {
    Route::get('/', function () { return view('login'); });
    Route::get('login', [AuthController::class, 'login'])->name('login');
    Route::post('login', [AuthController::class, 'login_check'])->name('login-check');
});

// Authenticated routes
Route::middleware(['auth'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('projects', [ProjectController::class, 'show_projects'])->name('projects');
    Route::get('tasks/{task_id}/', [TaskController::class, 'view_task'])->name('view-task');
});
```

### Project Management Routes
```php
Route::group(['middleware' => ['auth', 'superAdmin']], function () {
    // Projects
    Route::get('projects/add', [ProjectController::class, 'add_project'])->name('add-project');
    Route::post('projects/save', [ProjectController::class, 'save_project'])->name('save-project');
    Route::get('projects/{project_id}/edit', [ProjectController::class, 'edit_project'])->name('edit-project');
    Route::put('projects/{project_id}/update', [ProjectController::class, 'update_project'])->name('update-project');
    
    // Tasks
    Route::get('project/{project_id}/tasks/add', [DashboardController::class, 'add_task'])->name('add-task');
    Route::post('project/{project_id}/tasks/save', [DashboardController::class, 'save_task'])->name('save-task');
});
```

### Communication Endpoints

The system provides several AJAX endpoints that enable seamless communication between the frontend and backend without requiring full page reloads. These endpoints handle comment submissions, project data retrieval, and real-time updates, ensuring users can interact with the system smoothly and efficiently.

---

## How the System Works

### User Authentication and Access Flow

When a user logs into the SGF Portal, the system first validates their credentials against the database. Upon successful authentication, the system determines the user's role and access level, then redirects them to the appropriate dashboard. SuperAdmins and Admins are directed to the administrative dashboard with full system access, while Team Members see a focused task-oriented interface, and Client Members access a simplified project viewing portal.

The authentication system maintains user sessions securely and tracks user activity throughout their interaction with the platform. Each user's permissions are checked dynamically as they navigate through different sections, ensuring they only access features and data appropriate to their role.

### Project Management Workflow

Project management in SGF Portal follows a structured lifecycle that begins with project creation. When a new project is initiated, it receives a unique job code for identification and tracking purposes. The project is then linked to a specific client and assigned to relevant team members based on their expertise and availability.

Projects progress through defined phases including Define, Design, Code, Deploy, and Billing. Each phase has specific objectives, timelines, and deliverables that teams must complete before advancing to the next stage. The system tracks progress automatically and provides visual indicators of completion status.

Project managers can monitor resource allocation, track time spent on different phases, and adjust timelines as needed. The system maintains a complete audit trail of all project activities, changes, and communications for accountability and reporting purposes.

### Task Management System Operations

The task management system operates on a dynamic status-based workflow. Tasks are created within projects and assigned to specific team members with defined due dates and priority levels. The system categorizes tasks into various statuses including New, Urgent, In Progress, and Recently Finished.

The TaskOverview component provides real-time filtering and sorting capabilities, allowing users to view tasks by status, project, date, or alphabetical order. Users can search for specific tasks using project names or job codes, and the system provides instant results without page refreshes.

Task assignments trigger automatic notifications to relevant team members via email and in-system alerts. The system tracks task completion rates, identifies bottlenecks, and provides insights into team productivity and project progress.

### Real-time Communication Features

The SGF Portal implements real-time communication through Laravel Reverb WebSocket connections. When users post comments, update task statuses, or make project changes, these updates are immediately broadcast to all relevant team members without requiring page refreshes.

The comment system supports threaded conversations, allowing team members to have detailed discussions about specific tasks or projects. Users can mention other team members using @ symbols, which triggers immediate notifications to the mentioned users.

File attachments are seamlessly integrated into the communication system, allowing users to share documents, images, and other resources directly within task comments or project messages. The system automatically generates previews for supported file types and maintains version history.

### File Management and Storage

File management in SGF Portal is designed for security and efficiency. When users upload files, the system validates file types and sizes to prevent security risks and storage issues. Files are stored in organized directory structures based on projects and tasks for easy retrieval.

The system generates unique filenames to prevent conflicts and maintains metadata about each file including upload date, file size, and the user who uploaded it. File access is controlled based on user permissions and project assignments, ensuring sensitive information remains secure.

File downloads are tracked and logged for audit purposes, and the system provides search capabilities to help users quickly locate specific documents or resources within their accessible projects.

### Performance and Scalability

The SGF Portal is designed to handle growing teams and increasing project loads efficiently. The system implements intelligent caching strategies to reduce database queries and improve response times. Frequently accessed data such as project lists, user information, and status counts are cached and updated only when necessary.

Database queries are optimized using proper indexing strategies, particularly for frequently filtered columns like project status, task assignments, and creation dates. The system uses lazy loading techniques to display initial content quickly while loading additional data as needed.

The Livewire components implement pagination and progressive loading, allowing users to work with large datasets without experiencing performance degradation. Search operations are debounced to prevent excessive server requests while providing responsive user feedback.

### Security and Data Protection

Security in SGF Portal operates on multiple layers to protect sensitive project and client information. The authentication system uses secure password hashing and session management to prevent unauthorized access. User permissions are checked at both the route level and within individual components to ensure proper access control.

All user inputs are validated and sanitized to prevent security vulnerabilities such as SQL injection and cross-site scripting attacks. File uploads are restricted to safe file types and sizes, and all uploaded content is scanned for potential security threats.

The system maintains detailed audit logs of all user activities, including login attempts, data modifications, and file access. These logs help administrators monitor system usage and investigate any security incidents.

### Integration and Extensibility

The SGF Portal is built with extensibility in mind, allowing for future enhancements and integrations with external systems. The modular architecture enables developers to add new features without disrupting existing functionality.

The system provides hooks and events that can be used to integrate with external tools such as time tracking software, accounting systems, or customer relationship management platforms. API endpoints can be extended to support mobile applications or third-party integrations as business needs evolve.

The notification system is designed to support multiple channels including email, SMS, and push notifications, allowing organizations to customize communication preferences based on their specific requirements.

---

## Deep Dive: Technical Architecture

### Application Structure and Design Patterns

The SGF Portal follows Laravel's MVC (Model-View-Controller) architecture with additional layers for enhanced functionality. The application is structured around domain-driven design principles, where each major feature area (Projects, Tasks, Users, Clients) has its own set of models, controllers, and related components.

#### Controller Architecture

The controller layer is organized into specialized controllers that handle specific business domains:

**DashboardController**: Serves as the central hub for user interactions, managing the main dashboard display logic and routing users to appropriate interfaces based on their roles. It handles complex queries for task aggregation, project summaries, and user-specific data filtering.

**ProjectController**: Manages the complete project lifecycle including creation, updates, phase tracking, and archival. It implements sophisticated project filtering logic that considers user permissions, client relationships, and project status hierarchies.

**TaskController**: Handles task management operations with complex status workflows. The controller implements intelligent task assignment logic that considers user availability, skill sets, and workload distribution.

**AuthController**: Manages authentication flows with role-based redirection logic. It includes company selection functionality for multi-tenant scenarios and implements secure password reset workflows.

#### Model Relationships and Data Flow

The data model implements a sophisticated relationship structure that supports complex business scenarios:

**User Model Relationships**: Users maintain many-to-many relationships with projects, tasks, clients, and teams. The model includes dynamic access level checking that considers both role-based permissions and contextual access rights. The user model implements custom methods for checking permissions across different contexts and calculating user-specific data aggregations.

**Project Model Complexity**: Projects serve as the central entity connecting clients, phases, tasks, and team members. The model implements soft deletes with cascading logic that maintains data integrity while allowing for project recovery. Project models include complex querying methods for status tracking, progress calculation, and resource allocation analysis.

**Task Model Workflows**: Tasks implement a sophisticated status management system with time-based transitions. The model includes logic for handling "Recently Finished" tasks that automatically transition to "Past Finished" after 30 days. Task assignments support multiple users with notification cascading and workload balancing.

#### Livewire Component Architecture

The system extensively uses Livewire components for reactive user interfaces:

**TaskOverview Component**: This is the most complex component in the system, implementing real-time task filtering, sorting, and pagination. The component maintains multiple state variables for filtering (status, alphabet, year, month) and implements intelligent query optimization to prevent performance issues with large datasets.

The component uses computed properties extensively to cache expensive operations and implements debounced search functionality to reduce server load. It includes sophisticated billing month filtering that considers task creation dates and status transitions.

**ProjectTasks Component**: Manages project-specific task views with context-aware filtering. The component implements dynamic loading strategies that fetch additional data as users interact with the interface.

#### Event-Driven Architecture

The system implements a comprehensive event-driven architecture for real-time updates:

**CommentAdded Event**: Broadcasts real-time comment updates to all relevant users. The event includes sophisticated audience targeting that considers task assignments, project memberships, and notification preferences.

**Notification System**: Implements multiple notification channels (email, database, real-time) with intelligent routing based on user preferences and urgency levels. The system includes mention detection and automatic notification cascading for task updates.

### Database Architecture Deep Dive

#### Schema Design Philosophy

The database schema is designed for flexibility and performance, implementing several advanced patterns:

**Polymorphic Relationships**: Used for attachments and comments that can belong to multiple entity types. This allows for consistent file handling across projects, tasks, and messages.

**Pivot Table Enhancements**: Many-to-many relationships include additional metadata in pivot tables. For example, the project-user relationship includes assignment dates and role specifications, while project-phase relationships include duration and target information.

**Soft Delete Cascading**: Implements intelligent soft delete logic that maintains referential integrity while allowing for data recovery. When projects are soft deleted, related tasks remain accessible but are filtered from normal views.

#### Query Optimization Strategies

The system implements several query optimization techniques:

**Eager Loading Patterns**: Controllers use sophisticated eager loading strategies to prevent N+1 query problems. The TaskOverview component, for example, loads projects, statuses, and user relationships in optimized batches.

**Database-Level Filtering**: Complex filtering operations are pushed to the database level rather than being handled in application code. This is particularly evident in the TaskOverview component's filtering methods.

**Index Strategy**: The database includes strategic indexes on frequently queried columns, particularly for task filtering by project, status, and date ranges.

#### Migration Architecture

The migration system implements a chronological approach with careful dependency management:

**Foundation Migrations**: Early migrations establish core tables (roles, permissions, users) that other tables depend on.

**Feature Migrations**: Subsequent migrations add feature-specific tables with proper foreign key relationships.

**Enhancement Migrations**: Later migrations add columns and indexes to support new features while maintaining backward compatibility.

### Real-Time System Implementation

#### Laravel Reverb Integration

The real-time system uses Laravel Reverb for WebSocket communication:

**Channel Strategy**: The system uses both public and private channels. Task-specific channels (task.{id}) provide targeted updates, while user channels handle personal notifications.

**Event Broadcasting**: Events are carefully designed to include only necessary data to minimize bandwidth usage. The CommentAdded event, for example, includes comment data, user information, and task context.

**Connection Management**: The frontend implements reconnection logic and handles connection state changes gracefully, ensuring users remain connected even during network interruptions.

#### Frontend Integration

The JavaScript integration implements sophisticated connection management:

**Echo Configuration**: Uses environment-specific configuration for WebSocket connections with fallback strategies for different network conditions.

**Event Handling**: Implements event listeners that update the UI without full page reloads, maintaining user context and scroll positions.

**State Synchronization**: Ensures frontend state remains synchronized with backend data through careful event handling and state management.

### File Management System

#### Storage Architecture

The file management system implements a multi-layered approach:

**Storage Abstraction**: Uses Laravel's filesystem abstraction to support multiple storage backends (local, S3, etc.) without code changes.

**File Organization**: Implements a hierarchical storage structure that organizes files by project, task, and date for efficient retrieval and management.

**Security Layers**: Files are stored outside the web root with access controlled through application logic, ensuring unauthorized users cannot access files directly.

#### Upload Processing

File uploads implement several processing steps:

**Validation Pipeline**: Multiple validation layers check file types, sizes, and content to prevent security issues.

**Metadata Extraction**: The system extracts and stores file metadata including MIME types, sizes, and upload timestamps.

**Preview Generation**: For supported file types, the system generates previews and thumbnails for improved user experience.

### Security Implementation

#### Authentication Architecture

The authentication system implements multiple security layers:

**Password Security**: Uses Laravel's built-in password hashing with bcrypt and configurable rounds for enhanced security.

**Session Management**: Implements secure session handling with configurable timeouts and automatic cleanup.

**Multi-Factor Considerations**: The architecture supports future multi-factor authentication implementation.

#### Authorization Framework

The authorization system uses Spatie Laravel Permission with custom enhancements:

**Role Hierarchy**: Implements a hierarchical role system where higher roles inherit permissions from lower roles.

**Contextual Permissions**: Permissions are checked not just at the route level but also within components and views for fine-grained access control.

**Dynamic Permission Checking**: The system includes methods for checking permissions dynamically based on context, such as project membership or task assignment.

#### Data Protection

Data protection implements several strategies:

**Input Sanitization**: All user inputs are validated and sanitized to prevent XSS and injection attacks.

**Output Encoding**: Data is properly encoded when displayed to prevent script injection.

**CSRF Protection**: All forms include CSRF tokens to prevent cross-site request forgery attacks.

### Performance Optimization Deep Dive

#### Caching Strategy

The system implements a multi-level caching strategy:

**Query Result Caching**: Expensive database queries are cached with intelligent invalidation strategies.

**User-Specific Caching**: Cache keys include user identifiers to prevent data leakage between users.

**Tag-Based Invalidation**: Uses cache tags to invalidate related cached data when underlying data changes.

#### Database Performance

Database performance is optimized through several techniques:

**Query Analysis**: Regular analysis of slow queries using Laravel's query logging and database profiling tools.

**Index Optimization**: Strategic indexes on frequently queried columns, particularly for filtering and sorting operations.

**Connection Pooling**: Implements connection pooling for high-traffic scenarios to reduce connection overhead.

#### Frontend Performance

Frontend performance optimizations include:

**Asset Optimization**: CSS and JavaScript assets are minified and compressed for faster loading.

**Lazy Loading**: Components and data are loaded on-demand to reduce initial page load times.

**Debounced Operations**: Search and filter operations are debounced to reduce server load and improve user experience.

### Integration Architecture

#### API Design

The system is designed with API-first principles:

**RESTful Endpoints**: Follows REST conventions for consistent API design.

**Response Formatting**: Standardized JSON response formats for consistent client integration.

**Versioning Strategy**: Implements API versioning to support future enhancements without breaking existing integrations.

#### Third-Party Integration Points

The architecture supports various integration scenarios:

**Time Tracking**: Integration points for external time tracking systems.

**Accounting Systems**: Hooks for project billing and invoice generation.

**Communication Tools**: Integration capabilities for Slack, Microsoft Teams, and other communication platforms.

---

## System Maintenance and Monitoring

### Automated Health Monitoring

The SGF Portal includes comprehensive health monitoring capabilities that continuously check system components including database connectivity, cache performance, and queue processing status. These automated checks help identify potential issues before they impact user experience.

The system generates detailed logs of all activities, errors, and performance metrics. These logs are automatically rotated and archived to prevent storage issues while maintaining historical data for analysis and troubleshooting purposes.

Performance monitoring tracks response times, database query performance, and resource utilization to help administrators optimize system performance and plan for capacity upgrades as usage grows.

### Backup and Recovery Procedures

Data protection in SGF Portal includes automated backup procedures for both database content and uploaded files. Regular backups are created and stored securely to ensure business continuity in case of system failures or data corruption.

The backup system maintains multiple restore points, allowing administrators to recover data from specific dates if needed. Recovery procedures are documented and tested regularly to ensure rapid restoration of services when necessary.

File storage includes redundancy measures to prevent data loss, and the system can automatically switch to backup storage locations if primary storage becomes unavailable.

### Update and Maintenance Workflows

System updates are managed through a structured deployment process that minimizes downtime and ensures data integrity. The system supports rolling updates that allow new features to be deployed without interrupting ongoing work.

Maintenance procedures include regular dependency updates, security patches, and performance optimizations. The system provides maintenance mode capabilities that allow administrators to perform updates while displaying appropriate messages to users.

Database migrations are handled automatically during updates, ensuring schema changes are applied consistently across all environments while maintaining data integrity and backward compatibility where possible.

### Advanced Monitoring and Analytics

#### Application Performance Monitoring

The SGF Portal implements comprehensive performance monitoring that tracks multiple metrics across different system layers:

**Response Time Analysis**: The system continuously monitors response times for all endpoints, identifying slow-performing queries and operations. This data is aggregated to show trends over time and helps identify performance degradation before it impacts users.

**Database Query Performance**: All database queries are logged and analyzed for performance patterns. The system identifies N+1 query problems, missing indexes, and inefficient joins automatically.

**Memory Usage Tracking**: Server memory usage is monitored to identify memory leaks and optimize resource allocation. This includes tracking PHP memory usage, database connection pools, and cache memory consumption.

**User Experience Metrics**: Frontend performance metrics including page load times, JavaScript execution times, and user interaction response times are collected and analyzed.

#### Business Intelligence and Reporting

The system includes sophisticated reporting capabilities that provide insights into business operations:

**Project Performance Analytics**: Detailed analysis of project completion times, budget adherence, and resource utilization. The system tracks project phases and identifies bottlenecks in the workflow.

**Team Productivity Metrics**: Analysis of individual and team performance including task completion rates, comment activity, and collaboration patterns. This data helps managers identify high-performing team members and areas for improvement.

**Client Engagement Analysis**: Tracking of client portal usage, message response times, and project satisfaction indicators. This data helps improve client relationships and identify opportunities for additional services.

**Resource Allocation Optimization**: Analysis of team member workloads, skill utilization, and capacity planning. The system provides recommendations for optimal task assignments based on historical performance data.

#### Error Tracking and Debugging

Comprehensive error tracking helps maintain system reliability:

**Exception Monitoring**: All application exceptions are logged with full stack traces, user context, and environmental information. The system categorizes errors by severity and frequency to prioritize fixes.

**User Action Tracking**: Detailed logging of user actions helps reproduce bugs and understand user behavior patterns. This includes tracking failed operations, abandoned workflows, and user frustration indicators.

**Performance Bottleneck Identification**: The system automatically identifies performance bottlenecks including slow database queries, memory-intensive operations, and network latency issues.

**Automated Alert Systems**: Critical errors trigger immediate notifications to development teams, while performance degradation alerts help prevent issues before they impact users.

### Data Management and Analytics

#### Data Warehouse Architecture

The system implements a data warehouse approach for historical analysis:

**ETL Processes**: Extract, Transform, Load processes move data from operational systems to analytical databases for reporting and analysis.

**Data Retention Policies**: Automated data archival and cleanup processes ensure optimal database performance while maintaining historical data for analysis.

**Data Quality Monitoring**: Continuous monitoring of data quality including completeness, accuracy, and consistency checks.

#### Predictive Analytics

Advanced analytics capabilities provide predictive insights:

**Project Timeline Prediction**: Machine learning algorithms analyze historical project data to predict completion times and identify potential delays.

**Resource Demand Forecasting**: Analysis of historical workload patterns to predict future resource needs and capacity requirements.

**Risk Assessment**: Automated identification of projects at risk of delays or budget overruns based on historical patterns and current progress indicators.

#### Custom Reporting Framework

The system includes a flexible reporting framework:

**Report Builder**: Users can create custom reports using a drag-and-drop interface without requiring technical knowledge.

**Scheduled Reports**: Automated report generation and distribution via email or system notifications.

**Interactive Dashboards**: Real-time dashboards that update automatically as data changes, providing immediate insights into system performance and business metrics.

### Advanced Security Features

#### Threat Detection and Prevention

The system implements advanced security monitoring:

**Intrusion Detection**: Automated monitoring for suspicious activity patterns including unusual login attempts, data access patterns, and system usage anomalies.

**Vulnerability Scanning**: Regular automated scans for security vulnerabilities in application code, dependencies, and server configurations.

**Security Audit Trails**: Comprehensive logging of all security-related events including authentication attempts, permission changes, and data access patterns.

#### Data Privacy and Compliance

Robust data privacy features ensure regulatory compliance:

**Data Encryption**: All sensitive data is encrypted both at rest and in transit using industry-standard encryption algorithms.

**Access Logging**: Detailed logs of all data access including who accessed what data, when, and from where.

**Data Anonymization**: Capabilities for anonymizing sensitive data for testing and development purposes.

**Compliance Reporting**: Automated generation of compliance reports for various regulatory requirements.

#### Advanced Authentication Features

Sophisticated authentication capabilities enhance security:

**Single Sign-On (SSO)**: Integration capabilities with enterprise SSO systems including SAML and OAuth providers.

**Multi-Factor Authentication**: Support for various MFA methods including SMS, email, and authenticator apps.

**Session Management**: Advanced session management including concurrent session limits, idle timeouts, and device tracking.

**Password Policy Enforcement**: Configurable password policies including complexity requirements, expiration, and history tracking.

### Scalability and Infrastructure

#### Horizontal Scaling Architecture

The system is designed for horizontal scaling:

**Load Balancing**: Support for multiple application servers with intelligent load distribution based on server capacity and response times.

**Database Scaling**: Read replica support for distributing database load and improving query performance.

**Cache Clustering**: Distributed caching systems that can scale across multiple servers for improved performance.

**File Storage Scaling**: Support for distributed file storage systems that can handle growing file storage needs.

#### Cloud Infrastructure Integration

Cloud-native features for modern deployment scenarios:

**Container Support**: Docker containerization for consistent deployment across different environments.

**Auto-Scaling**: Integration with cloud auto-scaling services that automatically adjust resources based on demand.

**CDN Integration**: Content delivery network integration for faster file downloads and improved global performance.

**Backup and Disaster Recovery**: Automated backup systems with geographic distribution for disaster recovery scenarios.

#### Performance Optimization Strategies

Advanced performance optimization techniques:

**Query Optimization**: Automated query analysis and optimization recommendations based on actual usage patterns.

**Cache Warming**: Intelligent cache warming strategies that preload frequently accessed data.

**Resource Pooling**: Connection pooling and resource sharing to minimize overhead and improve efficiency.

**Background Processing**: Asynchronous processing of heavy operations to maintain responsive user interfaces.

---

## Business Impact and Benefits

### Improved Team Productivity

The SGF Portal significantly enhances team productivity by providing a centralized platform where all project-related activities occur. Team members no longer need to switch between multiple tools or search through email chains to find project information. The real-time nature of the system ensures everyone stays updated on project progress and changes instantly.

The task management system helps teams prioritize work effectively by clearly displaying urgent tasks and upcoming deadlines. The filtering and search capabilities allow team members to quickly focus on their specific responsibilities without being overwhelmed by information from other projects.

### Enhanced Client Communication

Client relationships are strengthened through the dedicated client portal that provides transparency into project progress. Clients can view project timelines, track milestone completion, and communicate directly with project teams through the integrated messaging system.

The file sharing capabilities ensure clients always have access to the latest project deliverables and can provide feedback directly within the system. This eliminates the confusion that often arises from email attachments and version control issues.

### Streamlined Project Management

Project managers benefit from comprehensive oversight tools that provide real-time visibility into all aspects of project execution. The phase-based tracking system helps ensure projects follow established methodologies and meet quality standards.

Resource allocation features help managers balance workloads across team members and identify potential bottlenecks before they impact project timelines. The reporting capabilities provide insights into team performance and project profitability.

### Data-Driven Decision Making

The SGF Portal collects comprehensive data about project execution, team performance, and client interactions. This data enables management to make informed decisions about resource allocation, process improvements, and business development opportunities.

Historical project data helps with more accurate estimation for future projects and identifies patterns that can be used to improve overall business operations.

## Future Development Roadmap

### Planned Enhancements

The SGF Portal development roadmap includes several exciting enhancements designed to further improve user experience and system capabilities. Mobile application development is planned to provide full system access from smartphones and tablets, enabling team members to stay connected and productive while away from their desks.

Advanced reporting and analytics features are being developed to provide deeper insights into project performance, team productivity, and business metrics. These features will include customizable dashboards, automated report generation, and predictive analytics capabilities.

Integration with popular third-party tools such as time tracking software, accounting systems, and customer relationship management platforms is planned to create a more comprehensive business management ecosystem.

### Scalability Improvements

As organizations grow, the SGF Portal will continue to evolve to meet increasing demands. Performance optimizations are continuously implemented to ensure the system remains responsive even with large numbers of users and projects.

Cloud deployment options are being developed to provide organizations with flexible hosting solutions that can scale automatically based on usage patterns. This will eliminate the need for organizations to manage their own server infrastructure while ensuring optimal performance.

### User Experience Enhancements

User interface improvements are continuously being developed based on user feedback and usability studies. These enhancements focus on making common tasks more efficient and reducing the learning curve for new users.

Accessibility improvements ensure the system remains usable by team members with different abilities and technical skill levels. This includes keyboard navigation support, screen reader compatibility, and customizable interface options.

## Conclusion

The SGF Portal represents a comprehensive solution for modern project management challenges. By combining real-time collaboration, robust task management, secure file sharing, and intelligent automation, the system enables organizations to deliver projects more efficiently while maintaining high quality standards.

The system's focus on user experience, security, and scalability ensures it can grow with organizations and adapt to changing business needs. The extensive documentation and support resources help teams maximize their investment in the platform and achieve their project management goals.

Through continuous development and improvement, the SGF Portal will continue to evolve as a leading project management solution that empowers teams to collaborate effectively and deliver exceptional results for their clients.

### Technical Implementation Highlights

#### Advanced Database Features

The SGF Portal leverages sophisticated database features for optimal performance and reliability:

**Transaction Management**: Complex operations use database transactions to ensure data consistency. For example, when creating a project with multiple team assignments, the entire operation is wrapped in a transaction to prevent partial data corruption.

**Soft Delete Implementation**: The system uses soft deletes extensively, allowing for data recovery while maintaining referential integrity. Deleted projects remain accessible for reporting purposes but are filtered from normal operations.

**Polymorphic Relationships**: Comments and attachments use polymorphic relationships, allowing them to be associated with multiple entity types (tasks, projects, messages) through a single, flexible structure.

**Database Indexing Strategy**: Strategic indexes are implemented on frequently queried columns, particularly for the TaskOverview component's complex filtering operations. Composite indexes optimize multi-column queries for better performance.

#### Livewire Component Optimization

The TaskOverview component demonstrates advanced Livewire techniques:

**Computed Properties**: Expensive operations like status counts and project filtering are cached using computed properties, reducing database queries and improving response times.

**Query String Synchronization**: Filter states are synchronized with the browser's query string, allowing users to bookmark and share filtered views.

**Pagination Integration**: Custom pagination logic works seamlessly with filtering and sorting, maintaining user context across page loads.

**Real-time Updates**: The component listens for WebSocket events and updates the interface without losing user's current filter and sort settings.

#### Event Broadcasting Architecture

The real-time system implements sophisticated broadcasting patterns:

**Channel Authorization**: Private channels use authorization callbacks to ensure users only receive updates for projects and tasks they have access to.

**Event Payload Optimization**: Events include only necessary data to minimize bandwidth usage while providing sufficient context for frontend updates.

**Presence Channels**: Future implementation will include presence channels to show which team members are currently active on specific projects.

**Queue Integration**: Broadcasting events are queued to prevent blocking user operations, ensuring responsive user interfaces even during high activity periods.

#### File Management Implementation

The file system demonstrates advanced Laravel filesystem usage:

**Storage Abstraction**: The system uses Laravel's filesystem abstraction, allowing seamless switching between local storage, S3, and other storage providers.

**File Validation**: Multi-layer validation includes MIME type checking, file size limits, and content scanning for security threats.

**Metadata Management**: File metadata is stored separately from the files themselves, enabling efficient searching and categorization.

**Access Control**: File access is controlled through application logic rather than direct web server access, ensuring proper permission checking.

#### Security Implementation Details

**Permission Middleware**: Custom middleware checks permissions at multiple levels, from route access to individual data record visibility.

**CSRF Protection**: All forms include CSRF tokens, and AJAX requests are automatically protected through Laravel's built-in CSRF handling.

**Input Sanitization**: User inputs are sanitized using Laravel's validation system and custom sanitization rules for rich text content.

**SQL Injection Prevention**: All database queries use parameter binding through Eloquent ORM, preventing SQL injection attacks.

### Development and Deployment Practices

#### Code Quality and Standards

The SGF Portal maintains high code quality through established development practices:

**Coding Standards**: The codebase follows PSR-12 coding standards for PHP and established Laravel conventions for consistent, readable code.

**Code Review Process**: All code changes go through peer review processes to ensure quality, security, and adherence to architectural principles.

**Automated Testing**: Comprehensive test suites include unit tests for models, feature tests for controllers, and browser tests for user workflows.

**Static Analysis**: Tools like PHPStan and Psalm are used to identify potential issues and maintain code quality standards.

#### Continuous Integration and Deployment

Modern DevOps practices ensure reliable deployments:

**Automated Testing Pipeline**: All code changes trigger automated test suites that must pass before deployment.

**Database Migration Testing**: Migration scripts are tested in staging environments that mirror production data structures.

**Zero-Downtime Deployments**: Deployment strategies ensure the system remains available during updates through rolling deployments and health checks.

**Rollback Capabilities**: Quick rollback procedures allow rapid recovery from deployment issues.

#### Environment Management

Sophisticated environment management supports development and production needs:

**Configuration Management**: Environment-specific configurations are managed through Laravel's configuration system and environment variables.

**Secrets Management**: Sensitive information like API keys and database passwords are managed through secure secret management systems.

**Environment Parity**: Development, staging, and production environments maintain consistency to prevent deployment issues.

**Feature Flags**: Feature flag systems allow for gradual rollout of new features and quick disabling of problematic functionality.

### Technical Debt Management

#### Code Maintenance Strategies

Proactive code maintenance prevents technical debt accumulation:

**Dependency Updates**: Regular updates of Laravel framework and third-party packages ensure security and performance improvements.

**Refactoring Cycles**: Scheduled refactoring cycles improve code quality and maintainability without disrupting feature development.

**Performance Monitoring**: Continuous monitoring identifies performance regressions and optimization opportunities.

**Documentation Updates**: Technical documentation is maintained alongside code changes to ensure accuracy and completeness.

#### Legacy System Integration

The system is designed to integrate with existing organizational systems:

**Data Migration Tools**: Utilities for migrating data from legacy project management systems.

**API Compatibility**: Backward-compatible APIs ensure existing integrations continue to function during system updates.

**Gradual Migration Support**: Features support gradual migration from legacy systems without disrupting ongoing projects.

### Future Technical Roadmap

#### Planned Technical Enhancements

**Microservices Architecture**: Future versions will support microservices architecture for improved scalability and maintainability.

**GraphQL API**: GraphQL endpoints will provide more efficient data fetching for mobile and single-page applications.

**Machine Learning Integration**: AI-powered features for project timeline prediction, resource optimization, and risk assessment.

**Advanced Analytics**: Real-time analytics dashboards with predictive insights and automated reporting capabilities.

#### Infrastructure Evolution

**Kubernetes Support**: Container orchestration for improved deployment flexibility and resource management.

**Multi-Region Deployment**: Geographic distribution for improved performance and disaster recovery capabilities.

**Edge Computing**: CDN integration and edge computing for faster global access to files and data.

**Serverless Components**: Serverless functions for specific operations like file processing and report generation.

### Key Success Factors

**Modular Architecture**: The system's modular design allows for independent development and testing of features, reducing complexity and improving maintainability.

**Real-time Collaboration**: WebSocket-powered live updates create a collaborative environment where team members can see changes as they happen, improving coordination and reducing conflicts.

**Intelligent Caching**: Multi-level caching strategies ensure optimal performance while maintaining data consistency across user sessions and system components.

**Comprehensive Audit Trail**: Every action in the system is logged, providing complete visibility into project history and enabling detailed analysis of team performance and project progress.

**Flexible Permission System**: The role-based permission system can be easily extended to support new user types and access patterns as organizational needs evolve.

**Scalable Infrastructure**: The system's architecture supports both vertical and horizontal scaling, allowing it to grow with organizational needs without requiring major architectural changes.

**API-First Design**: All functionality is accessible through well-designed APIs, enabling future mobile applications and third-party integrations without system modifications.

**Event-Driven Architecture**: The event system enables loose coupling between components and supports future enhancements like workflow automation and external system integration.

---

_SGF Portal Documentation - Version 2.0_  
_Last Updated: December 2024_  
_Technical Contact: support@shieldssgf.com_  
_Business Contact: info@shieldssgf.com_
