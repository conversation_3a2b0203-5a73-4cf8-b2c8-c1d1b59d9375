# Laravel Boost Guidelines

The Laravel Boost guidelines are specifically curated by Laravel maintainers for this application. These guidelines should be followed closely to enhance the user's satisfaction building Laravel applications.

## Foundational Context
This application is a Laravel application and its main Laravel ecosystems package & versions are below. You are an expert with them all. Ensure you abide by these specific packages & versions.

- php - <?php echo e(PHP_VERSION); ?>

<?php $__currentLoopData = app(\Laravel\Roster\Roster::class)->packages()->unique(fn ($package) => $package->rawName()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
- <?php echo e($package->rawName()); ?> (<?php echo e($package->name()); ?>) - v<?php echo e($package->majorVersion()); ?>

<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php if(! empty(config('boost.purpose'))): ?>
Application purpose: <?php echo config('boost.purpose'); ?>

<?php endif; ?>

## Conventions
- You must follow all existing code conventions used in this application. When creating or editing a file, check sibling files for the correct structure, approach, naming.
- Use descriptive names for variables and methods. For example, ___SINGLE_BACKTICK___isRegisteredForDiscounts___SINGLE_BACKTICK___, not ___SINGLE_BACKTICK___discount()___SINGLE_BACKTICK___.
- Check for existing components to reuse before writing a new one.

## Verification Scripts
- Do not create verification scripts or tinker when tests cover that functionality and prove it works. Unit and feature tests are more important.

## Application Structure & Architecture
- Stick to existing directory structure - don't create new base folders without approval.
- Do not change the application's dependencies without approval.

## Frontend Bundling
- If the user doesn't see a frontend change reflected in the UI, it could mean they need to run ___SINGLE_BACKTICK___npm run build___SINGLE_BACKTICK___, ___SINGLE_BACKTICK___npm run dev___SINGLE_BACKTICK___, or ___SINGLE_BACKTICK___composer run dev___SINGLE_BACKTICK___. Ask them.

## Replies
- Be concise in your explanations - focus on what's important rather than explaining obvious details.

## Documentation Files
- You must only create documentation files if explicitly requested by the user.
<?php /**PATH C:\Users\<USER>\SGF-Portal\storage\framework\views/9a0c0278f13e8e024d186877b8a1f4f4.blade.php ENDPATH**/ ?>