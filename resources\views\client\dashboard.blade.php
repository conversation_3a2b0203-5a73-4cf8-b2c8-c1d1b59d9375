@extends('layout.app')
@section('title', 'Dashboard')
@section('content')
    
    
    
    <section class="client-header main-header">
			<div class="container-xxl">
				<hr class="mt-0 mb-4 border-white" />
				<div class="meta d-flex justify-content-between align-items-center">
					<div class="copy">
						<h1 class="text-white mb-0">
							Welcome to your project hub,<br />
							<i>{{$user->name}}</i>
						</h1>
					</div>
					<div class="logo">
						@if ($client->logo && Storage::exists($client->logo))
                            <a href="#">
                                <img src="{{ Storage::url($client->logo) }}" alt="Client Logo" width="72" height="80" />
                            </a>
                        @endif

					</div>
				</div>
			</div>
		</section>
		<section class="client-project pt-0">
			<div class="container-xxl">
				<hr class="mt-0 mb-4 border-white" />
				<div class="row projects-row">
					<div class="col-md-4 project-column mb-5">
						<h2 class="text-uppercase">ACTIVE Projects</h2>
						<hr class="mt-0 mb-4 border-white" />
                        <div class="project-list">                
							@forelse ($activeProjects as $active)
								<div class="meta-project d-flex {{ $active['colorClass'] }} hover" style="--phase-color: {{ $active['phaseColor'] }};">
									<div class="icon-wrap d-flex align-items-center justify-content-center me-3">
										@php
											$percentage = 0;
											if ($lastPhaseId > 0 && isset($active['currentPhase'])) {
												$percentage = ($active['currentPhase']->id / $lastPhaseId) * 100;
											}
										@endphp
										<div class="progress-bar" data-steps="40" style="--value: {{ $percentage }}; --color: {{ $active['phaseColor'] }};"></div>
										<div class="icon d-flex align-items-center justify-content-center">
											<img src="{{ $active['phaseIcon'] }}" alt="Phase {{ $active['currentPhaseOrder'] }}" width="22" height="22"/>
										</div>
										<div class="over">
											<h2 class="mb-0">{{ number_format($percentage, 0) }}%</h2>
										</div>
									</div>
									<div class="copy">
										<h2 class="mb-1">
											<a href="{{ route('track-project', ['id' => $active['project']->id]) }}"
                                             style="color: {{ $active['phaseColor'] }};">
												[{{ $active['project']->job_code }}] - PHASE {{ $active['currentPhaseOrder'] }}/{{ $active['totalPhases'] }}
											</a>
										</h2>
										<p class="text-white">{{ $active['project']->name }}</p>
										<div class="cta-row">
											<a href="#">
												Project Details
											</a>
										</div>
									</div>
								</div>
							@empty
								<div class="text-center text-muted py-4">
									<p>No active projects found.</p>
								</div>
							@endforelse
						</div>
					</div>
					<div class="col-md-4 project-column mb-5">
						<h2 class="text-uppercase">VOYAGER</h2>
						<hr class="mt-0 mb-4 border-white" />
						<div class="project-list">

                            @foreach ($voyagerProjects as $voyagerProject)
                                
                          
								<div class="meta-project d-flex orange">
									<div class="icon-wrap d-flex align-items-center justify-content-center me-3">
										<div class="border-bar"></div>
										<div class="icon d-flex align-items-center justify-content-center"><img src="images/deploy-project-icon.svg" alt="" /></div>
									</div>
									<div class="copy">
										<h2 class="mb-1"><a href="{{ route('track-project', ['id' => $voyagerProject->id]) }}">[{{$voyagerProject->job_code}}]</a></h2>
										<p class="text-white">{{$voyagerProject->name}}</p>
										<div class="cta-row"><a href="#">Site Analytics</a><a href="#">Billing History</a></div>
									</div>
								</div>
							@endforeach
						</div>
					</div>
					<div class="col-md-4 project-column mb-5">
						<div class="align-items-center d-flex justify-content-between">
							<h2 class="text-uppercase">ARCHIVED Projects</h2>
							<a class="cta link border-0 p-0 mt-0 mb-2" href="{{ route('client.projects') }}">See All</a>
						</div>
						<hr class="mt-0 mb-4 border-white" />
						<div class="project-list">

                            @foreach ($archivedProjects as $archivedProject )
                                
                           
							<div class="meta-project d-flex orange">
								<div class="icon d-flex align-items-center justify-content-center checked"><img src="images/checked-project-icon.svg" alt="" /></div>
								<div class="copy">
									<h2 class="mb-1"><a href="{{ route('track-project', ['id' => $archivedProject->id]) }}">[{{$archivedProject->job_code}}]</a></h2>
									<p class="text-white">{{$archivedProject->name}}</p>
									<div class="cta-row"><a href="#">Project History</a><a href="#">Billing History</a></div>
								</div>
							</div>
                             @endforeach
					</div>
				</div>
			</div>
		</section>

        @endsection

        @push('styles')



        @endpush