@extends('layout.app')
@section('title', 'View Task')
@section('content')



    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">

            @if(admin_superadmin_permissions())
            <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-tasks') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Tasks List</a>

            @else

            <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('show-all-tasks') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Tasks List</a>

            @endif
        </div>
        <div class="meta d-flex">
            <div class="heading-title d-flex align-items-center">
                @if($task->status->name == 'Urgent')
                <div class="icon-star me-2">
                    <img src="{{ asset('images/star-icon.svg') }}" alt="" height="30" width="30" />
                </div>
                @endif
                <h1 class="heavy text-white mb-0">{{ $task->project->job_code }} {{ $task->project->name }} <span class="text-uppercase">({{ $task->status->name }})</span>  <span class="text-uppercase">({{ $task->created_at->format('M d Y') }})</span></h1>
            </div>
        </div>
    </div>
</section>
    <section class="client-project project-dashboard">
        <div class="container-xxl">
            <div class="row projects-row">
                <div class="col-md-3 col-xl-2 project-column quick-links">
                    <div class="head d-flex justify-content-between">
                        <h2 class="text-uppercase">STATUS</h2>
                        <div class="icon-more"><i class="bi bi-three-dots"></i></div>
                    </div>
                    <hr class="mt-0 mb-4 border-white" />
                    <div class="statuses-list d-flex flex-column">
                        @foreach ($statuses as $status)
                            @php
                                $isAssigned = (int) $task->status_id === $status->id;
                                $isRecentlyFinished = strtolower($status->name) === 'recently finished';
                            @endphp

                        <button class="flag-status w-75 {{ $isAssigned ? 'assigned' : '' }}" data-status_id="{{ $status->id }}">
                            {{ ($isRecentlyFinished && $isAssigned) ? 'Completed' : ($isRecentlyFinished ? 'Mark as Completed' : $status->name) }}
                        </button>


                    @endforeach

                    </div>
                    <div class="head d-flex justify-content-between">
                        <h2 class="text-uppercase mt-5">Assigned to</h2>
                        <div class="icon-more mt-5"><i class="bi bi-three-dots"></i></div>
                    </div>
                    <hr class="mt-0 mb-4 border-white" />
                    <div class="assigned-to-list">
                        @foreach ($users as $user)
                            <div class="meta d-flex align-items-center">
                                <input type="checkbox" class="me-1 assigned_to_checkbox" name="assigned_to[]"
                                    id="{{ $user->id }}" @if ($task->users->contains($user->id)) checked @endif><label
                                    for="{{ $user->id }}"> {{ $user->name }} </label>
                            </div>
                        @endforeach
                        <div class="assign_to_users_error text-danger"></div>
                    </div>
                    <div class="link-to-project mt-5"><a href="#">Link to Project Page</a></div>
                </div>
                <div class="col-md-9 col-xl-10 project-column task-view">
                    <div class="head d-flex justify-content-between align-items-center">


                        <h2 class="text-uppercase m-0 w-75">{{ $task->name }} - {{$task->admin->name}}</h2>
                        {{-- <div class="add-time ms-4 mb-2" position="relative" onclick="openDatePicker(event)">
                            <div class="meta-label @if($task->due_date != null) border-0 @endif d-flex align-items-center">
                                <i class="bi bi-clock me-2"></i>
                                <span class="text-white @if ($task->due_date == null) fs-6 @endif label-text">
                                    @if ($task->due_date != null)
                                        {{ \Carbon\Carbon::parse($task->due_date)->format('M d') }}
                                    @else
                                        ADD DUE DATE
                                    @endif
                                </span>
                            </div>
                            <input class="due-date date-picker" id="dueDate" type="date" name="due-date" value="{{ $task->due_date }}"/>
                        </div> --}}

                        <div class="time add-time w-25 text-center" onclick="openDatePicker(event)" style="cursor: pointer;">
                            <div class="meta-label border-0 m-0 px-0">
                                <i class="bi bi-clock me-2"></i>
                                <span class="text-white label-text">
                                    @if($task->due_date != null)
                                        {{ \Carbon\Carbon::parse($task->due_date)->format('M d') }}
                                    @else
                                        Add Due Date
                                    @endif
                                </span>
                            </div>
                            <input class="due-date date-picker" id="dueDate" type="date" name="due-date" value="{{ $task->due_date }}"/>
                        </div>

                    </div>
                    <hr class="mt-0 mb-4 border-white" />
                    <div class="mt-0 mb-4 p-4">
                        <div class="icon-user task-user"><img
                                src="{{ set_user_image($task->admin->profile_image) }}"alt="" /></div>
                        <div class="task_details rounded">                            <div class='task_description position-relative' id="task_description{{ $task->id }}">
                                <span class="task-timestamp">{{ $task->created_at->format('M d, Y g:i A') }}</span>
                                {!! $task->description !!}
                            </div>
                            @if ($task->taskAttachments->isNotEmpty())
                                <div class="attachment_container scrollbar" style="display: flex; flex-wrap: wrap; align-items: center; margin-top: 10px; width: 99.3%;">
                                    <span class="me-2"> Attachments ({{ $task->taskAttachments->count() }}): </span>
                                    {{-- Assuming you want to display all attachments, including the "main" one if it was migrated --}}
                                    @foreach($task->taskAttachments as $attachment)
                                        @php
                                            $fileName = $attachment->task_attachment;
                                            $fileUrl = Storage::url('images/' . $fileName);
                                            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
                                            $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
                                            $isImage = in_array(strtolower($extension), $imageExtensions);
                                        @endphp

                                        <div class='task_attachment attachment-item me-2 mb-2 preview_img_span'
                                            data-img_url="{{ $fileUrl }}"
                                            title="{{ $fileName }}"
                                            style="cursor: pointer; display: inline-block; border: 1px solid rgb(108, 117, 125); border-radius: 5px; padding: 5px; margin-right: 10px; text-align: center; max-width: 150px;">

                                            <div class="attachment_preview">
                                                @if($isImage)
                                                    <img src="{{ $fileUrl }}" class="" style="height: 50px; width: auto; margin:auto">
                                                @else
                                                    {{-- File type icon --}}
                                                    @php
                                                        $icon = 'fa-file'; // default
                                                        if (in_array($extension, ['pdf'])) $icon = 'fa-file-pdf text-danger';
                                                        elseif (in_array($extension, ['doc', 'docx'])) $icon = 'fa-file-word text-primary';
                                                        elseif (in_array($extension, ['xls', 'xlsx'])) $icon = 'fa-file-excel text-success';
                                                        elseif (in_array($extension, ['zip', 'rar'])) $icon = 'fa-file-archive text-warning';
                                                        elseif (in_array($extension, ['txt'])) $icon = 'fa-file-alt text-muted';
                                                        elseif (in_array($extension, ['svg'])) $icon = 'fa-file-svg';
                                                    @endphp
                                                    <i class="fas {{ $icon }}" style="font-size: 48px;"></i>
                                                @endif

                                                <div class="attachment-name text-dark text-truncate" style="max-width: 130px;">{{ $fileName }}</div>
                                            </div>
                                        </div>
                                    @endforeach

                                </div>
                            @endif
                        </div>

                        <!-- Comment section moved above the comment input -->
                        <div class="comment-section mb-3">
                            <div class="no_comments_row text-center text-white">No Comments Yet..</div>
                            <div class="comment_rows text-white d-none"></div>
                        </div>

                        <!-- Comment input section below the comments -->
                        <div class="message-task comment-input-section mb-3">
                            <div class="icon-user">
                                <img class='' src="{{ set_user_image(Auth::user()->profile_image) }}"
                                    alt="" />
                            </div>
                            <div class="meta-text">
                                <input type="hidden" name="_token" value="{{ csrf_token() }}">


                                <div class="rich-editor-container">

                                    <div class="formatting-toolbar">
                                        <div class="format-tip">
                                            💡 Tip: Use <strong>Ctrl+B</strong> for bold, <strong>Ctrl+I</strong> for
                                            italic, and <strong>Ctrl+U</strong> for underline. Press the same keys again to
                                            remove the format.
                                        </div>
                                    </div>


                                    <div id="comment-input" class="comment-input text-white rich-editor"
                                        contenteditable="true" placeholder="Add comment... @mention users"></div>

                                    <!-- Hidden textarea to store HTML content for submission -->
                                    <textarea id="comment-input-hidden" name="comment-input-hidden" style="display: none;"></textarea>
                                </div>

                                <!-- Mention suggestions dropdown moved outside the editor container for better positioning -->
                            </div>

                            <div id="mention-suggestions" class="mention-suggestions d-none"
                                style="position: fixed; display: none; z-index: 9999;">
                                <ul class="list-unstyled mb-0"></ul>
                            </div>
                        </div>
                    </div>


                    <div id="dropArea">

                        <div class="drag-overlay" id="dragOverlay">
                            <div>
                                <i class="bi bi-cloud-arrow-down me-2"></i>
                                Drop files to attach
                            </div>
                        </div>

                    </div>

                        <div class="cta-row d-flex justify-content-between px-5">
                            <div class="upload-btn-wrapper">
                                <button class="btn-upload text-uppercase text-white d-flex align-items-center"><i class="bi bi-upload me-2"></i> Upload Files</button>
                                <input type="file" name="myfile" multiple/>
                            </div>
                            <button id="mark-complete" data-id="{{$task->id}}" class="cta text-uppercase mt-0 me-1 submit-btn" type="submit">MARK COMPLETE</button>
                            <button id="add-comment" class="cta text-uppercase mt-0 submit-comment-btn" type="submit">POST COMMENT</button>
                        </div>

                        
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content p-1">
                    <div class="modal-header border-0">
                        <div class="modal-heading"></div>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body d-flex justify-content-center align-items-center">
                        <img id="" class="modal-image img-fluid" src="" alt="Full Size Image"
                            style='height:450px; width:auto'>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
    <!-- Font Awesome for file icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>

    /* IMP => change the background color of checkbox */
    input[type=checkbox]{
        accent-color: #ff5811;
    }


.upload-btn-wrapper {
      position: relative;
      overflow: hidden;
      display: inline-block;
    }
    
    .btn-upload {
      background-color: #0d6efd;
      border-radius: 4px;
      padding: 0.5rem 1.5rem;
      font-weight: 500;
      cursor: pointer;
      border: none;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
      transition: all 0.2s ease;
    }
    
    .btn-upload:hover {
      background-color: #0b5ed7;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
    
    .upload-btn-wrapper input[type=file] {
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
    
    /* Comment attachment styles */
    .comment_attachments {
      display: flex;
      align-items: center;
      padding: 0px 10px;
      height: 50px;
      overflow-y: hidden;
      margin-top: 8px;
      flex-wrap: nowrap;
      overflow-x: auto;
    }
    
    .attachment-item {
      display: flex;
      align-items: center;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 4px 8px;
      margin-right: 8px;
      white-space: nowrap;
      animation: fadeIn 0.3s ease;
    }
    
    .attachment-icon {
      margin-right: 5px;
      color: #6c757d;
    }
    
    .attachment-name {
      font-size: 0.875rem;
      margin-right: 5px;
      max-width: 150px;
      overflow: hidden;
      width:70px;
      text-overflow: ellipsis;
    }
    
    .attachment-remove {
      color: #dc3545;
      background: none;
      border: none;
      font-size: 0.875rem;
      padding: 0;
      cursor: pointer;
      opacity: 0.7;
    }
    
    .attachment-remove:hover {
      opacity: 1;
    }
    
   
    
    .drag-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(13, 110, 253, 0.05);
      border: 2px dashed #0d6efd;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #0d6efd;
      font-weight: 500;
      z-index: 10;
      display: none;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(5px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .scrollbar::-webkit-scrollbar {
      height: 4px;
    }
    
    .scrollbar::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    
    .scrollbar::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 2px;
    }
    
    .scrollbar::-webkit-scrollbar-thumb:hover {
      background: #555;
    }










        .format-tip strong {
            color: white;
        }

        .format-tip {
            background-color: #1e1e1e;
            border-left: 4px solid #e69010;
            padding: 10px 15px;
            margin-top: 10px;
            font-size: 14px;
            border-radius: 6px;
            color: #ddd;
            font-family: 'Segoe UI', sans-serif;
        }

        /* width */
        .scrollbar::-webkit-scrollbar {
            height: 5px;
            width: 5px;
            opacity: 0.5;
        }

        /* Track */
        .scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 50px;
        }

        /* Handle */
        .scrollbar::-webkit-scrollbar-thumb {
            background: #ff5811;
            border-radius: 30px;
        }

        .preview_img,
        .preview_img_span {
            cursor: pointer;
        }

        .label-text {
            font: 700 28px "Futura Std", serif;
        }

        .modal-dialog {
            height: 200px;
        }

        .upload-btn-wrapper {
            width: 100%;
            color: white;
        }

        .comment-section {
            /* padding: 10px 0px; */
            /* min-height: 100px; */
            position: relative;
            /* margin-bottom: 20px; */
            display: flex;
            flex-direction: column;
        }        .task_details {
            color: white;
            background-color: #282828;
            margin-bottom: 10px;
            margin-left: 15px;
            padding: 20px 20px 20px 20px;
        }

        .task-timestamp {
            position: absolute;
            right: 20px;
            top: -20px;
            color: #ff4c00;
            font-size: 0.85em;
            white-space: nowrap;
            z-index: 1;
            background-color: #282828;
            padding: 2px 8px;
            border-radius: 4px;
        }

        .task_attachment {
            display: inline-block;
            padding: 5px;
            border: 1px solid grey;
            border-radius: 5px;
            margin-top: 5px;
        }

        .message-task {
            padding: 0px !important;

        }

        .icon-user,
        .task-user {
            position: relative;
        }

        .icon-user {
            height: 2.5rem !important;
            left: -5px !important;
            top: 15px !important;
            width: 2.5rem !important;
            overflow: hidden;
            border-radius: 50%;
        }

        .comment_row {
            margin-left: 15px;
            padding: 20px 20px 20px 30px;
            margin-bottom: 10px;
            border-radius: 10px;
            background-color: #282828;
            display: flex;
            align-items: flex-start;
            position: relative;
        }

        .comment_body {
            margin-left: 10px;
            width: 100%;
            position: relative;
        }

        .comment-text {
            width: 98%;
            padding-right: 100px;
        }        .comment-timestamp {
            position: absolute;
            right: 20px;
            top: -10px;
            color: #ff4c00;
            font-size: 0.8em;
            white-space: nowrap;
            z-index: 1;
        }

        .dropdown-container {
            position: absolute;
            right: 45px;
            top: 5px;
            box-shadow: 0px 0px 15px 0px #ff4c00;
            background-color: #282828;
            color: white;
            width: 100px;
            border: 1px solid #ff4c00;
            z-index: 1;
        }

        .dropdown-links {
            display: block;
            color: white;
            text-decoration: none;
            padding: 5px;
        }

        .dropdown-links:hover {
            background-color: grey
        }

        .comment-input,
        .comment-input:focus {
            border: none;
            outline: none;
            background-color: #282828;
            width: 100%;
            padding: 5px 15px;
            resize: none;
            color: white;
        }

        .comment-img {
            position: absolute;
            margin-left: -45px;
            border: 1px solid white;
        }

        .meta-text {
            margin-left: 15px;
            padding: 1rem 2rem !important;
        }

        .cta {
            background-color: #ff4c00;
            color: white;
            width: max-content;
            text-wrap: nowrap;
        }

        .mention-suggestions {
            position: absolute;
            background: #282828;
            border: 1px solid #ff4c00;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            width: 100%;
            margin-top: 5px;
        }

        .mention-suggestions ul li {
            padding: 5px 10px;
            cursor: pointer;
            color: white;
        }

        .mention-suggestions ul li:hover {
            background: #ff4c00;
        }

        .mention-suggestions ul li.selected {
            background: #ff4c00;
        }

        .comment-input {
            position: relative;
        }

        .tagged-user {
            color: #ff4c00;
            font-weight: bold;
        }

        .flag-status {
            cursor: pointer;
        }

        .flag-status:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .comment_options_icon {
            position: relative;
            top: -19px;
            left: 16px;
            cursor: pointer;
            color: #ff4c00 !important;
            z-index: 10;
        }

        .save-edit-btn,
        .cancel-edit-btn {
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 25px;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-right: 10px;
            margin-top: 1rem;
        }

        .save-edit-btn {
            background-color: #28a745;
            color: white;
        }

        .save-edit-btn:hover {
            background-color: #218838;
        }

        .cancel-edit-btn {
            background-color: #dc3545;
            color: white;
        }

        .cancel-edit-btn:hover {
            background-color: #c82333;
        }


        .save-edit-btn,
        .cancel-edit-btn {
            display: inline-block;
        }

        /* Rich text editor styles for comment editing */
        .edit-comment-container {
            margin-bottom: 10px;
            width: 100%;
        }

        .edit-comment-container .formatting-toolbar {
            background-color: #333;
            border: 1px solid #ff4c00;
            border-bottom: none;
            padding: 5px;
            border-radius: 4px 4px 0 0;
            display: flex;
            gap: 5px;
        }

        .edit-comment-container .format-btn {
            background-color: #444;
            border: none;
            color: white;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 3px;
        }

        .edit-comment-container .format-btn:hover {
            background-color: #ff4c00;
        }

        .edit-comment-container .format-btn.active {
            background-color: #ff4c00;
            color: #fff;
            box-shadow: 0 0 8px rgba(255, 76, 0, 0.8);
            transform: scale(1.1);
            font-weight: bold;
            border: 2px solid white;
        }

        .edit-comment-container .rich-editor {
            background-color: #282828;
            border: 1px solid #ff4c00;
            border-radius: 0 0 4px 4px;
            color: white;
            min-height: 80px;
            padding: 8px;
            width: 100%;
            outline: none;
        }


        #mention-suggestions ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .mention-item {
            display: flex;
            align-items: center;
            padding: 8px;
            cursor: pointer;
        }

        .mention-item:hover {
            background-color: #f0f0f0;
        }

        .mention-item-content {
            display: flex;
            align-items: center;
        }

        .mention-item-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .mention-item.selected {
            background-color: #d3d3f4;
        }

        #mention-suggestions {
            position: absolute;
            background-color: #343a40;
            border: 1px solid #495057;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            max-height: 200px;
            overflow-y: auto;
            z-index: 9999;
            width: 300px;
        }

        /* Style for mentioned users - ONLY the mention itself should be orange */
        .mentioned-user {
            color: #ff4c00 !important;
            font-weight: bold !important;
        }

        /* Ensure proper styling for mentioned users in comments */
        .comment-text .mentioned-user {
            color: #ff4c00 !important;
            font-weight: bold !important;
        }

        /* Comment rows display with older at top, newer at bottom */
        .comment_rows {
            display: flex;
            flex-direction: column;
            min-height: 50px;
            width: 100%;
        }









        /* Highlight effect for new comments */
        @keyframes highlight-fade {
            0% {
                background-color: rgba(255, 76, 0, 0.2);
            }

            100% {
                background-color: transparent;
            }
        }

        .highlight-comment {
            animation: highlight-fade 2s ease-out;
        }

        /* Style for @mentions in the comment text */
        .comment-text a.mention {
            color: #ff4c00;
            font-weight: bold;
            text-decoration: none;
        }

        /* Style for @mentions as they're being typed */
        .comment-input::placeholder {
            color: #6c757d;
        }

        /* Make sure the mentions in the displayed comments are orange */
        .comment-text span.mentioned-user {
            color: #ff4c00;
            font-weight: bold;
        }

        /* Ensure the comment text itself remains white and line breaks are preserved */
        .comment-text {
            color: white;
            white-space: pre-wrap;
            /* Preserve line breaks */
            word-wrap: break-word;
            /* Break long words */
            overflow-wrap: break-word;
            /* Ensure text wraps properly */
        }

        /* Ensure proper spacing for comment rows */
        .comment_rows {
            padding-bottom: 10px;
        }

        /* Add spacing between individual comments */
        .comment_row {
            margin-bottom: 15px;
            width: 100%;
        }

        /* Attachment styling */
        .attachment-item {
            transition: transform 0.2s ease;
        }

        .attachment-item:hover {
            transform: scale(1.05);
            box-shadow: 0 0 10px rgba(255, 76, 0, 0.3);
        }

        .attachment-name {
            font-size: 0.8rem;
            margin-top: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 140px;
        }

        .attachment-preview img {
            border: 1px solid #6c757d;
            background-color: #343a40;
        }

        .attachment-icon {
            color: #ff4c00;
            padding: 5px;
        }

        /* Rich Text Editor Styles */
        .rich-editor-container {
            border: 1px solid #444;
            border-radius: 5px;
            overflow: hidden;
            background-color: #282828;
        }

        .formatting-toolbar {
            display: flex;
            padding: 8px;
            background-color: #333;
            border-bottom: 1px solid #444;
        }

        .format-btn {
            background: none;
            border: none;
            color: #ccc;
            margin-right: 10px;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .format-btn:hover {
            background-color: #444;
            color: #fff;
        }

        .format-btn.active {
            background-color: #ff4c00;
            color: #fff;
            box-shadow: 0 0 8px rgba(255, 76, 0, 0.8);
            transform: scale(1.1);
            font-weight: bold;
            border: 2px solid white;
        }

        .rich-editor {
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
            padding: 10px 15px;
            line-height: 1.5;
            outline: none;
            white-space: pre-wrap;
            /* Preserve line breaks */
            word-wrap: break-word;
            /* Break long words */
        }

        .rich-editor:empty:before {
            content: attr(placeholder);
            color: #6c757d;
            pointer-events: none;
        }

        .rich-editor a,
        .detected-link {
            color: #ff4c00;
            text-decoration: underline;
        }

        .link-dialog {
            position: absolute;
            background-color: #282828;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            width: 300px;
            z-index: 1000;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        }
    </style>
@endpush




@push('script')
    <script>
        //markComplete
        let markCompleteButton = document.getElementById('mark-complete');
        markCompleteButton.addEventListener('click', function(e) {

            let taskId = this.getAttribute('data-id');

            markCompleteButton.disabled = true;
            markCompleteButton.classList.add('loading');

            $.ajax({
                url: "{{ route('mark-complete') }}",
                method: "POST",
                data: {
                    _token: "{{ csrf_token() }}",
                    task_id: taskId
                },
                success: function(response) {
                    markCompleteButton.disabled = false;
                    markCompleteButton.classList.remove('loading');
                    window.location.reload();
                }
            })



        });
    </script>

    <script>
        function restoreCaretPosition(containerEl, offset) {
            if (!containerEl) {
                console.error("Invalid container element");
                return false;
            }

            if (typeof offset !== 'number' || isNaN(offset) || offset < 0) {
                console.error("Invalid offset value:", offset);
                offset = 0; // Default to beginning as a fallback
            }

            let charIndex = 0;
            let foundPosition = false;
            const range = document.createRange();
            const sel = window.getSelection();

            if (!sel) {
                console.error("Selection API not available");
                return false;
            }

            // Handle the case when the editor is empty
            if (containerEl.childNodes.length === 0) {
                try {
                    range.setStart(containerEl, 0);
                    range.collapse(true);
                    sel.removeAllRanges();
                    sel.addRange(range);

                    // Store this position for future use
                    $commentInput.data('lastCursorPosition', range.cloneRange());
                    return true;
                } catch (e) {
                    console.error("Error setting cursor in empty container:", e);
                    return false;
                }
            }

            function traverseNodes(node) {
                if (foundPosition) return;

                if (node.nodeType === Node.TEXT_NODE) {
                    const nodeLength = node.length || 0;
                    const nextCharIndex = charIndex + nodeLength;

                    if (offset >= charIndex && offset <= nextCharIndex) {
                        try {
                            // Ensure the position within the node is valid
                            const nodeOffset = Math.min(offset - charIndex, nodeLength);
                            range.setStart(node, nodeOffset);
                            range.collapse(true);
                            sel.removeAllRanges();
                            sel.addRange(range);

                            // Store this position for future use
                            $commentInput.data('lastCursorPosition', range.cloneRange());

                            foundPosition = true;
                        } catch (e) {
                            console.error("Error setting cursor position:", e);
                            // Try to recover by setting to start of node
                            try {
                                range.setStart(node, 0);
                                range.collapse(true);
                                sel.removeAllRanges();
                                sel.addRange(range);

                                // Store this position for future use
                                $commentInput.data('lastCursorPosition', range.cloneRange());

                                foundPosition = true;
                            } catch (recoveryError) {
                                console.error("Failed to recover cursor position");
                            }
                        }
                    }
                    charIndex = nextCharIndex;
                } else {
                    const childNodes = node.childNodes;
                    for (let i = 0; i < childNodes.length; i++) {
                        traverseNodes(childNodes[i]);
                        if (foundPosition) break;
                    }
                }
            }

            try {
                traverseNodes(containerEl);

                // If we couldn't restore the exact position, try to place cursor at a reasonable position
                if (!foundPosition) {
                    // First try to find a text node
                    const textNodes = [];
                    const walker = document.createTreeWalker(
                        containerEl,
                        NodeFilter.SHOW_TEXT,
                        null,
                        false
                    );

                    let node;
                    while (node = walker.nextNode()) {
                        textNodes.push(node);
                    }

                    if (textNodes.length > 0) {
                        // Find a suitable text node - prefer one in the middle rather than at the beginning
                        const targetNode = textNodes[Math.min(textNodes.length - 1, 1)];
                        range.setStart(targetNode, 0);
                        range.collapse(true);
                    } else {
                        // If no text nodes, place at the end of the editor
                        range.selectNodeContents(containerEl);
                        range.collapse(false); // false means collapse to end
                    }

                    sel.removeAllRanges();
                    sel.addRange(range);

                    // Store this position for future use
                    $commentInput.data('lastCursorPosition', range.cloneRange());
                }

                return true;
            } catch (e) {
                console.error("Error in restoreCaretPosition:", e);

                // Ultimate fallback: try to place cursor at the end
                try {
                    range.selectNodeContents(containerEl);
                    range.collapse(false); // false means collapse to end
                    sel.removeAllRanges();
                    sel.addRange(range);

                    // Store this position for future use
                    $commentInput.data('lastCursorPosition', range.cloneRange());

                    return true;
                } catch (fallbackError) {
                    console.error("Complete failure in cursor positioning:", fallbackError);
                    return false;
                }
            }
        }






        // Cache DOM elements for better performance
        let $commentInput = null;
        let commentInputElement = null;
        let selectedUsers = new Set();

        // Initialize and cache references when document is ready
        function initMentionSystem() {
            $commentInput = $('#comment-input');
            if ($commentInput.length === 0) {
                console.error("Comment input element not found");
                return false;
            }
            commentInputElement = $commentInput[0];


            return true;
        }


        function sanitizeHtml(html) {
            if (!html || typeof html !== 'string') {
                return '';
            }

            try {
                // Create a temporary div to handle the HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;



                return tempDiv.innerHTML;
            } catch (e) {
                console.error("Error sanitizing HTML:", e);
                // Return safe fallback on error
                return html.replace(/</g, '&lt;').replace(/>/g, '&gt;');
            }
        }

        /**
         * Updates the set of mentioned users
         */
        function updateMentionedUsers() {
            if (!$commentInput) {
                if (!initMentionSystem()) return;
            }

            try {
                // Clear current selection set
                selectedUsers = new Set();

                // Find all mentioned user elements
                const mentionedElements = $commentInput.find('.mentioned-user');

                // Add each mentioned user to the selectedUsers set
                mentionedElements.each(function() {
                    const userId = $(this).data('user-id');
                    if (userId) {
                        selectedUsers.add(userId);
                    }
                });

                console.log("Updated mentioned users:", Array.from(selectedUsers));
            } catch (e) {
                console.error("Error updating mentioned users:", e);
            }
        }


        function findNodeAndOffsetFromCharacterOffset(container, targetOffset) {
            if (!container || targetOffset < 0) {
                return {
                    node: container,
                    offset: 0
                };
            }

            let currentOffset = 0;
            let foundNode = null;
            let foundOffset = 0;

            function traverse(node) {
                if (foundNode) return;

                if (node.nodeType === Node.TEXT_NODE) {
                    const length = node.textContent.length;
                    if (currentOffset <= targetOffset && targetOffset <= currentOffset + length) {
                        foundNode = node;
                        foundOffset = targetOffset - currentOffset;
                    }
                    currentOffset += length;
                } else {
                    for (let i = 0; i < node.childNodes.length && !foundNode; i++) {
                        traverse(node.childNodes[i]);
                    }
                }
            }

            traverse(container);

            // If not found, return the last possible position
            if (!foundNode) {
                const lastTextNode = findLastTextNode(container);
                if (lastTextNode) {
                    return {
                        node: lastTextNode,
                        offset: lastTextNode.textContent.length
                    };
                } else {
                    return {
                        node: container,
                        offset: container.childNodes.length
                    };
                }
            }

            return {
                node: foundNode,
                offset: foundOffset
            };
        }


        function findLastTextNode(container) {
            if (!container) return null;

            if (container.nodeType === Node.TEXT_NODE) {
                return container;
            }

            for (let i = container.childNodes.length - 1; i >= 0; i--) {
                const lastNode = findLastTextNode(container.childNodes[i]);
                if (lastNode) return lastNode;
            }

            return null;
        }


        // function restoreCaretPosition(containerEl, offset) {
        //     try {
        //         if (!containerEl) {
        //             console.error("Invalid container element");
        //             return;
        //         }

        //         const sel = window.getSelection();
        //         if (!sel) {
        //             console.error("Selection API not available");
        //             return;
        //         }

        //         // Handle empty container case
        //         if (containerEl.childNodes.length === 0) {
        //             const range = document.createRange();
        //             range.setStart(containerEl, 0);
        //             range.collapse(true);
        //             sel.removeAllRanges();
        //             sel.addRange(range);
        //             return;
        //         }

        //         // Find the appropriate node and offset
        //         const position = findNodeAndOffsetFromCharacterOffset(containerEl, offset);

        //         try {
        //             // Apply the selection
        //             const range = document.createRange();
        //             range.setStart(position.node, position.offset);
        //             range.collapse(true);
        //             sel.removeAllRanges();
        //             sel.addRange(range);
        //         } catch (e) {
        //             console.error("Error restoring position:", e);
        //             // Fallback: place cursor at the beginning
        //             try {
        //                 const range = document.createRange();
        //                 range.setStart(containerEl, 0);
        //                 range.collapse(true);
        //                 sel.removeAllRanges();
        //                 sel.addRange(range);
        //             } catch (fallbackError) {
        //                 console.error("Complete failure restoring cursor position");
        //             }
        //         }
        //     } catch (e) {
        //         console.error("Error in restoreCaretPosition:", e);
        //     }
        // }


        function cleanupRemovedMentions() {
            if (!$commentInput) {
                if (!initMentionSystem()) return;
            }

            // Set flag to prevent recursive cleanup
            if (window.isCleaningUpMentions) {
                console.warn("Cleanup already in progress, skipping");
                return;
            }

            window.isCleaningUpMentions = true;

            try {
                // Store selection state
                const selection = window.getSelection();
                let currentCursorPosition = null;
                let caretOffset = 0;

                // Check if we have a stored cursor position from a selection deletion
                const storedPosition = $commentInput.data('lastCursorPosition');

                if (selection && selection.rangeCount > 0) {
                    try {
                        // Use the current selection if it's in the editor
                        const range = selection.getRangeAt(0);
                        if ($commentInput[0].contains(range.commonAncestorContainer)) {
                            currentCursorPosition = range.cloneRange();

                            // Calculate character offset for position restoration
                            const preCaretRange = range.cloneRange();
                            preCaretRange.selectNodeContents(commentInputElement);
                            preCaretRange.setEnd(range.startContainer, range.startOffset);
                            caretOffset = preCaretRange.toString().length;
                        } else if (storedPosition && $commentInput[0].contains(storedPosition.commonAncestorContainer)) {
                            // If current selection is outside editor but we have a stored position, use that
                            currentCursorPosition = storedPosition;

                            // Calculate character offset for the stored position
                            const preCaretRange = storedPosition.cloneRange();
                            preCaretRange.selectNodeContents(commentInputElement);
                            preCaretRange.setEnd(storedPosition.startContainer, storedPosition.startOffset);
                            caretOffset = preCaretRange.toString().length;
                        }
                    } catch (e) {
                        console.error("Error saving selection state:", e);

                        // Try to use stored position as fallback
                        if (storedPosition && $commentInput[0].contains(storedPosition.commonAncestorContainer)) {
                            currentCursorPosition = storedPosition;
                        }
                    }
                } else if (storedPosition && $commentInput[0].contains(storedPosition.commonAncestorContainer)) {
                    // No current selection but we have a stored position
                    currentCursorPosition = storedPosition;

                    // Calculate character offset for the stored position
                    try {
                        const preCaretRange = storedPosition.cloneRange();
                        preCaretRange.selectNodeContents(commentInputElement);
                        preCaretRange.setEnd(storedPosition.startContainer, storedPosition.startOffset);
                        caretOffset = preCaretRange.toString().length;
                    } catch (e) {
                        console.error("Error calculating offset for stored position:", e);
                    }
                }

                // Get current HTML content
                const editorHtml = $commentInput.html();

                // Process the HTML using DOM methods rather than regex for better reliability
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = editorHtml;

                // Track if any changes were made
                let changesMade = false;

                // Step 1: Process specific problematic patterns using a more structured approach
                cleanupMentionPatterns(tempDiv);

                // Step 2: Handle special formatting and @ symbols
                cleanupFormatting(tempDiv);

                // Step 3: Clean up text nodes to prevent duplicate @ symbols
                cleanupTextNodes(tempDiv);

                // Get the final HTML
                const cleanedHtml = tempDiv.innerHTML;

                // Only update if changes were made
                if (cleanedHtml !== editorHtml) {
                    changesMade = true;

                    // Temporarily remove the input event handler
                    const oldInputHandler = $commentInput.data('inputHandler');
                    if (oldInputHandler) {
                        $commentInput.off('input', oldInputHandler);
                    }

                    // Update the HTML
                    $commentInput.html(cleanedHtml);

                    // Check if we're in the middle of deleting a mention
                    const isDeletingMention = $commentInput.data('isDeletingMention');

                    // Restore cursor position if not deleting a mention
                    if (!isDeletingMention) {
                        // First look for a deletion marker
                        const marker = $commentInput.find('#deletion-marker')[0];

                        if (marker) {
                            // Position at the marker
                            try {
                                const range = document.createRange();
                                range.setStartAfter(marker);
                                range.setEndAfter(marker);

                                // Remove the marker
                                marker.parentNode.removeChild(marker);

                                // Apply the selection
                                selection.removeAllRanges();
                                selection.addRange(range);
                            } catch (e) {
                                console.error("Error positioning at marker:", e);
                                // Fallback to stored position
                                if (currentCursorPosition) {
                                    restoreFromSavedPosition(selection, currentCursorPosition, caretOffset);
                                }
                            }
                        } else if (currentCursorPosition) {
                            // Use stored position
                            restoreFromSavedPosition(selection, currentCursorPosition, caretOffset);
                        }
                    }

                    // Restore the input handler with a delay to prevent immediate triggering
                    if (oldInputHandler) {
                        setTimeout(function() {
                            $commentInput.on('input', oldInputHandler);
                        }, 10);
                    }

                    // Update mentioned users after changes
                    updateMentionedUsers();
                }
            } catch (e) {
                console.error("Error in cleanupRemovedMentions:", e);
            } finally {
                // Always reset the flag, even if an error occurs
                setTimeout(function() {
                    window.isCleaningUpMentions = false;
                }, 20);
            }
        }

        /**
         * Helper function to restore position from saved range or offset
         */
        function restoreFromSavedPosition(selection, savedRange, caretOffset) {
            try {
                // First check if the saved range is still valid
                if (savedRange && savedRange.startContainer && savedRange.startContainer.parentNode) {
                    // Check if the range is within the editor
                    if (commentInputElement.contains(savedRange.commonAncestorContainer)) {
                        selection.removeAllRanges();
                        selection.addRange(savedRange);

                        // Store this position for future use
                        $commentInput.data('lastCursorPosition', savedRange.cloneRange());
                        return true;
                    }
                }

                // If we get here, the saved range wasn't valid or wasn't in the editor
                console.warn("Saved range is no longer valid, falling back to offset method");
                return restoreCaretPosition(commentInputElement, caretOffset);
            } catch (e) {
                console.error("Error restoring cursor from range:", e);
                // Fall back to offset method
                return restoreCaretPosition(commentInputElement, caretOffset);
            }
        }

        /**
         * Clean up problematic patterns in the DOM structure
         * @param {Element} container - Container element to process
         */
        function cleanupMentionPatterns(container) {
            // Look for the specific pattern: <div>@</div> followed by mention span
            const divElements = container.querySelectorAll('div');

            for (let i = 0; i < divElements.length; i++) {
                const div = divElements[i];

                // Check if this div contains just an @ symbol
                if (div.textContent === '@') {
                    // Look at the next sibling
                    const nextSibling = div.nextSibling;

                    if (nextSibling &&
                        nextSibling.nodeType === Node.ELEMENT_NODE &&
                        nextSibling.classList.contains('mentioned-user')) {
                        // Remove the @ div since it's redundant
                        div.parentNode.removeChild(div);
                    }
                }
            }
        }

        /**
         * Clean up formatting issues
         * @param {Element} container - Container element to process
         */
        function cleanupFormatting(container) {
            // Handle elements with orange color style that aren't proper mentions
            const styledElements = container.querySelectorAll('[style*="color: #ff4c00"]');
            for (let i = 0; i < styledElements.length; i++) {
                const element = styledElements[i];
                if (!element.classList.contains('mentioned-user')) {
                    element.style.color = '';
                    // Remove leading @ symbol if present
                    if (element.textContent.startsWith('@')) {
                        element.textContent = element.textContent.substring(1);
                    }
                }
            }

            // Handle font elements with orange color
            const fontElements = container.querySelectorAll('font[color="#ff4c00"]');
            for (let i = 0; i < fontElements.length; i++) {
                const element = fontElements[i];
                if (!element.classList.contains('mentioned-user')) {
                    // Replace with text content without font tag and @ symbol
                    const textContent = element.textContent.replace(/^@/, '');
                    const textNode = document.createTextNode(textContent);
                    element.parentNode.replaceChild(textNode, element);
                }
            }

            // Process other formatted elements with @ symbols
            processFormattedElements(container, 'b');
            processFormattedElements(container, 'span');
            processFormattedElements(container, 'font');
        }

        /**
         * Process formatted elements of a specific type
         * @param {Element} container - Container element
         * @param {string} tagName - Tag name to process
         */
        function processFormattedElements(container, tagName) {
            const elements = container.getElementsByTagName(tagName);

            // Note: we need to use a reverse loop since we're potentially modifying the collection
            for (let i = elements.length - 1; i >= 0; i--) {
                const element = elements[i];

                // Skip if this is a proper mention
                if (element.classList.contains('mentioned-user')) {
                    continue;
                }

                // Check if this element contains just an @ symbol
                if (element.textContent === '@') {
                    // Replace with nothing
                    element.parentNode.removeChild(element);
                }
                // Check if it starts with @ but has more content
                else if (element.textContent.startsWith('@')) {
                    // Remove the @ symbol but keep the rest of the content
                    element.textContent = element.textContent.substring(1);
                }
            }
        }

        /**
         * Clean up text nodes to prevent duplicate @ symbols
         * @param {Element} container - Container element
         */
        function cleanupTextNodes(container) {
            const walker = document.createTreeWalker(
                container,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const nodesToProcess = [];
            let node;

            // Collect text nodes first (because we'll modify them)
            while (node = walker.nextNode()) {
                nodesToProcess.push(node);
            }

            // Process the collected nodes
            for (let i = 0; i < nodesToProcess.length; i++) {
                const node = nodesToProcess[i];

                // If this text node is right before a mention span and ends with @
                if (node.nextSibling &&
                    node.nextSibling.nodeType === Node.ELEMENT_NODE &&
                    (node.nextSibling.classList.contains('mentioned-user') ||
                        node.nextSibling.textContent.startsWith('@'))) {

                    // Remove trailing @ symbols with proper whitespace handling
                    node.textContent = node.textContent.replace(/@\s*$/, '');
                }

                // Also handle repeated @ symbols
                if (node.textContent.includes('@@')) {
                    node.textContent = node.textContent.replace(/@@+/g, '@');
                }
            }
        }



        // Function to check if HTML content is complex and needs simplification
        function isComplexHtml(html) {
            // Check for complex HTML structures that might come from Word, Google Docs, etc.
            const complexPatterns = [
                /<\!--\[if/i, // MS Word conditional comments
                /<o:/i, // MS Word XML namespace
                /<w:/i, // MS Word XML namespace
                /<meta[^>]+content="Word\./i, // Word metadata
                /<style[^>]*>[\s\S]*?<\/style>/i, // Style tags
                /<table/i, // Tables
                /<div style="[^"]*mso-/i, // MSO styles
                /<span style="[^"]*mso-/i, // MSO styles
                /<font/i, // Font tags
                /<img/i // Image tags (except our own)
            ];

            // Check if any complex patterns are found
            for (let i = 0; i < complexPatterns.length; i++) {
                if (complexPatterns[i].test(html)) {
                    return true;
                }
            }

            // Also check if the HTML is unusually long
            if (html.length > 1000) {
                return true;
            }

            return false;
        }

        // Function to simplify HTML content for pasting
        function simplifyHtmlContent(html, plainText) {
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');

            try {
                // Try to parse the HTML
                tempDiv.innerHTML = html;

                // Remove unwanted elements
                const unwantedTags = ['style', 'script', 'meta', 'link', 'head', 'title', 'iframe'];
                unwantedTags.forEach(tag => {
                    const elements = tempDiv.getElementsByTagName(tag);
                    for (let i = elements.length - 1; i >= 0; i--) {
                        elements[i].parentNode.removeChild(elements[i]);
                    }
                });

                // Simplify complex structures
                simplifyElement(tempDiv);

                // Get the simplified HTML
                let simplifiedHtml = tempDiv.innerHTML;

                // If the result is still complex or empty, fall back to plain text
                if (isComplexHtml(simplifiedHtml) || !simplifiedHtml.trim()) {
                    // Format plain text with line breaks
                    return plainText.replace(/\n/g, '<br>');
                }

                return simplifiedHtml;
            } catch (error) {
                console.error('Error simplifying HTML:', error);
                // Fall back to plain text with line breaks preserved
                return plainText.replace(/\n/g, '<br>');
            }
        }

        // Helper function to recursively simplify an element and its children
        function simplifyElement(element) {
            // Skip if this is null or not an element
            if (!element || element.nodeType !== Node.ELEMENT_NODE) {
                return;
            }

            // Process all child nodes first (in reverse to avoid issues with live collections)
            const children = Array.from(element.childNodes);
            for (let i = children.length - 1; i >= 0; i--) {
                const child = children[i];

                if (child.nodeType === Node.ELEMENT_NODE) {
                    // Recursively simplify child elements
                    simplifyElement(child);
                }
            }

            // Remove all attributes except for a few allowed ones
            const allowedAttrs = ['href', 'target', 'class'];
            const attrs = Array.from(element.attributes);

            for (let i = attrs.length - 1; i >= 0; i--) {
                const attr = attrs[i];
                if (!allowedAttrs.includes(attr.name)) {
                    element.removeAttribute(attr.name);
                }
            }

            // Convert specific elements to more appropriate ones
            if (element.tagName === 'DIV') {
                // Only add line breaks between divs if they contain text
                if (element.textContent.trim() && element.previousElementSibling) {
                    element.insertBefore(document.createElement('br'), element.firstChild);
                }
            }

            // Remove empty elements (except for br tags)
            if (element.tagName !== 'BR' && !element.textContent.trim() && !element.querySelector('img, br')) {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            }
        }

        // Function to insert content at the current cursor position
        function insertContentAtCursor(content) {
            const editor = $('#comment-input');
            const selection = window.getSelection();

            // Focus the editor
            editor.focus();

            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);

                // Check if the selection is within the editor
                if (editor[0].contains(range.commonAncestorContainer)) {
                    // Delete any selected content
                    range.deleteContents();

                    // Create a temporary div to hold the content
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = content;

                    // Insert each node from the content
                    const fragment = document.createDocumentFragment();
                    while (tempDiv.firstChild) {
                        fragment.appendChild(tempDiv.firstChild);
                    }

                    range.insertNode(fragment);

                    // Move cursor to the end of the inserted content
                    range.collapse(false);
                    selection.removeAllRanges();
                    selection.addRange(range);

                    // Store this position for future use
                    editor.data('lastCursorPosition', range.cloneRange());
                } else {
                    // If selection is outside editor, append to the end
                    editor.append(content);
                    placeCaretAtEnd(editor[0]);
                }
            } else {
                // No selection, append to the end
                editor.append(content);
                placeCaretAtEnd(editor[0]);
            }
        }

        // We're using the existing placeCaretAtEnd function defined elsewhere in the code

        // Function to auto-detect and format links in the comment
        function autoDetectLinks() {
            const editor = $('#comment-input');
            const editorHtml = editor.html();

            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = editorHtml;

            // Find all text nodes
            const textNodes = [];
            const walker = document.createTreeWalker(
                tempDiv,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            // Process each text node to find and replace URLs
            let hasChanges = false;

            // URL regex pattern - more robust to handle query parameters
            const urlPattern = /https?:\/\/[^\s<"']+/g;

            textNodes.forEach(textNode => {
                const text = textNode.textContent;
                const matches = text.match(urlPattern);

                if (matches) {
                    // Create a document fragment to hold the new content
                    const fragment = document.createDocumentFragment();
                    let lastIndex = 0;

                    // Process each URL match
                    matches.forEach(url => {
                        const startIndex = text.indexOf(url, lastIndex);

                        // Add text before the URL
                        if (startIndex > lastIndex) {
                            fragment.appendChild(document.createTextNode(
                                text.substring(lastIndex, startIndex)
                            ));
                        }

                        // Create the link element
                        const link = document.createElement('a');
                        link.href = url;
                        link.target = '_blank';
                        link.className = 'detected-link';
                        link.textContent = url;

                        // Add the link to the fragment
                        fragment.appendChild(link);

                        // Update lastIndex
                        lastIndex = startIndex + url.length;
                    });

                    // Add any remaining text
                    if (lastIndex < text.length) {
                        fragment.appendChild(document.createTextNode(
                            text.substring(lastIndex)
                        ));
                    }

                    // Replace the text node with the fragment
                    if (textNode.parentNode) {
                        textNode.parentNode.replaceChild(fragment, textNode);
                        hasChanges = true;
                    }
                }
            });

            // Only update if changes were made
            if (hasChanges) {
                editor.html(tempDiv.innerHTML);
            }
        }
    </script>
    <script type="module">
        const assignedUsers = {!! json_encode($users) !!};

        let allMentionableUsers = [...assignedUsers];
        const users = {!! json_encode($users) !!};
        let selectedUsers = new Set();
        let mentionStart = -1;
        let currentMention = '';
        let selectedIndex = -1;
        let selectedFiles = [];
        let formData = new FormData();

        let dragAndDroppedFiles = [];


        $(document).ready(function() {

            // Make the meta-text div accept drag and drop
            const dropArea = $('#dropArea');
            const dragOverlay = $('#dragOverlay');
            const fileInput = $('#fileInput');

            // Keep track of how many files we have
            let fileCount = 0;
            let dragCounter = 0; // Track drag enter/leave events

            // Handle file input change
            fileInput.on('change', function(event) {
                handleFiles(this.files);
            });

            // Prevent default drag behaviors on the entire document
            $(document).on('dragenter dragover dragleave drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
            });

            // Handle drag enter events - increment counter
            $(document).on('dragenter', function(e) {
                dragCounter++;
                dragOverlay.fadeIn(200);
            });

            // Handle drag leave events - decrement counter
            $(document).on('dragleave', function(e) {
                dragCounter--;
                if (dragCounter === 0) {
                    dragOverlay.fadeOut(200);
                }
            });

            // Reset drag counter on drop
            $(document).on('drop', function(e) {
                dragCounter = 0;
                dragOverlay.fadeOut(200);
                const files = e.originalEvent.dataTransfer.files;
                handleFiles(files);

            });

            // Handle the files (from either drag or input)
            function handleFiles(files) {
                if (files.length === 0) return;

                // Get or create attachments container
                let commentAttachmentsDiv = $('.meta-text').find('.comment_attachments');

                // Create the container if it doesn't exist
                if (commentAttachmentsDiv.length === 0) {
                    commentAttachmentsDiv = $("<div class='comment_attachments'></div>").addClass("scrollbar").css({
                        "padding": "0px 10px",
                        "height": "50px",
                        "overflow-y": "hidden",
                        "display": "flex",
                        "align-items": "center"
                    });
                    $('.meta-text').append(commentAttachmentsDiv);
                }

                Array.from(files).forEach(file => {
                    const fileId = 'file-' + (++fileCount);

                    // Store the original File object along with its ID
                    dragAndDroppedFiles.push({
                        file: file, // Store the actual File object
                        _fileId: fileId
                    });

                    updateFileInput();

                    const attachmentItem = $('<div></div>')
                        .addClass('attachment-item')
                        .attr('id', fileId)
                        .css('display', 'flex'); // Ensure display is set to flex

                    const icon = getFileIcon(file.name);
                    attachmentItem.append(`<span class="attachment-icon">${icon}</span>`);

                    attachmentItem.append(
                        `<span class="attachment-name" title="${file.name}">${file.name}</span>`);

                    // Add remove button
                    const removeBtn = $(
                        '<button class="attachment-remove"><i class="bi bi-x"></i></button>');
                    removeBtn.on('click', function() {
                        // Remove file from dragAndDroppedFiles array
                        const fileName = $(this).prev('.attachment-name').attr('title');
                        dragAndDroppedFiles = dragAndDroppedFiles.filter(f => f.file.name !==
                            fileName);

                        // Update the file input
                        updateFileInput();

                        attachmentItem.fadeOut(300, function() {
                            $(this).remove();

                            // If no attachments left, remove the container
                            if (commentAttachmentsDiv.children().length === 0) {
                                commentAttachmentsDiv.remove();
                            }
                        });
                    });

                    attachmentItem.append(removeBtn);

                    // Force a reflow to ensure the element is properly rendered
                    // Add to container and ensure it's visible
                    commentAttachmentsDiv.append(attachmentItem);

                    // Force repaint/reflow to ensure proper rendering
                    void attachmentItem[0].offsetHeight;
                });

                // Clear the file input
                fileInput.val('');

                // Update the file input with selected files
                updateFileInput();

                // Reset drag counter and ensure overlay is hidden
                dragCounter = 0;
                dragOverlay.fadeOut(200);
            }

            function getFileIcon(fileName) {
                const extension = fileName.split('.').pop().toLowerCase();

                const iconMap = {
                    'pdf': '<i class="bi bi-file-earmark-pdf"></i>',
                    'doc': '<i class="bi bi-file-earmark-word"></i>',
                    'docx': '<i class="bi bi-file-earmark-word"></i>',
                    'xls': '<i class="bi bi-file-earmark-excel"></i>',
                    'xlsx': '<i class="bi bi-file-earmark-excel"></i>',
                    'ppt': '<i class="bi bi-file-earmark-ppt"></i>',
                    'pptx': '<i class="bi bi-file-earmark-ppt"></i>',
                    'jpg': '<i class="bi bi-file-earmark-image"></i>',
                    'jpeg': '<i class="bi bi-file-earmark-image"></i>',
                    'png': '<i class="bi bi-file-earmark-image"></i>',
                    'gif': '<i class="bi bi-file-earmark-image"></i>',
                    'svg': '<i class="bi bi-file-earmark-image"></i>',
                    'zip': '<i class="bi bi-file-earmark-zip"></i>',
                    'rar': '<i class="bi bi-file-earmark-zip"></i>',
                    'txt': '<i class="bi bi-file-earmark-text"></i>',
                    'mp3': '<i class="bi bi-file-earmark-music"></i>',
                    'mp4': '<i class="bi bi-file-earmark-play"></i>',
                    'csv': '<i class="bi bi-file-earmark-spreadsheet"></i>'
                };

                return iconMap[extension] || '<i class="bi bi-file-earmark"></i>';
            }

            //paste any content into comment input box with enhanced handling
            $('#comment-input').on('paste', function(e) {
                // Get clipboard data
                const clipboardData = e.clipboardData || e.originalEvent.clipboardData;
                const items = clipboardData.items;
                let imageFound = false;
                let htmlContent = '';
                let plainText = '';

                // Check for HTML content in clipboard
                try {
                    htmlContent = clipboardData.getData('text/html');
                    plainText = clipboardData.getData('text/plain');
                } catch (error) {
                    console.error('Error reading clipboard data:', error);
                }

                // First check for images - highest priority
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        imageFound = true;

                        const blob = items[i].getAsFile();
                        const mimeType = blob.type; // e.g., "image/png", "image/svg+xml"

                        // Map MIME to proper extension
                        const mimeToExt = {
                            'image/png': 'png',
                            'image/jpeg': 'jpg',
                            'image/jpg': 'jpg',
                            'image/gif': 'gif',
                            'image/webp': 'webp',
                            'image/svg+xml': 'svg',
                            'image/bmp': 'bmp',
                            'image/tiff': 'tiff',
                            'image/x-icon': 'ico'
                        };

                        const extension = mimeToExt[mimeType] || mimeType.split('/')[1].replace('+xml', '');

                        const fileName = `pasted-image-${Date.now()}.${extension}`;

                        // Create a File object from the blob
                        const file = new File([blob], fileName, {
                            type: mimeType
                        });

                        // Add to selected files
                        selectedFiles.push(file);

                        // Create preview
                        createAttachmentPreview(file);
                        updateFileInput();

                        // Prevent default paste behavior for images
                        e.preventDefault();
                        break;
                    }
                }

                // If no image was found, handle text content
                if (!imageFound) {
                    // Check if it's a URL (simple check for http:// or https://)
                    if (plainText && (plainText.trim().startsWith('http://') || plainText.trim().startsWith(
                            'https://'))) {
                        // It's a URL - let the default paste happen, then format it as a link
                        setTimeout(function() {
                            autoDetectLinks();
                            cleanupRemovedMentions();
                        }, 0);
                    }
                    // Check if it's complex HTML content that needs simplification
                    else if (htmlContent && isComplexHtml(htmlContent)) {
                        e.preventDefault(); // Prevent default paste

                        // Simplify and insert the content
                        const simplifiedContent = simplifyHtmlContent(htmlContent, plainText);

                        // Insert at cursor position
                        insertContentAtCursor(simplifiedContent);

                        // Clean up after insertion
                        setTimeout(function() {
                            autoDetectLinks();
                            cleanupRemovedMentions();
                        }, 0);
                    }
                    // For simple text, let the default paste happen and clean up after
                    else {
                        setTimeout(function() {
                            autoDetectLinks();
                            cleanupRemovedMentions();
                        }, 0);
                    }
                }
            });

            // Helper function to create attachment preview for pasted images
            function createAttachmentPreview(file) {
                let commentAttachmentsDiv = $('.meta-text').find('.comment_attachments');

                // Create the container if it doesn't exist
                if (commentAttachmentsDiv.length === 0) {
                    commentAttachmentsDiv = $("<div class='comment_attachments'></div>").addClass("scrollbar").css({
                        "padding": "0px 10px",
                        "height": "50px",
                        "overflow-y": "hidden",
                        "display": "flex",
                        "align-items": "center"
                    });
                    $('.meta-text').append(commentAttachmentsDiv);
                }

                // Create preview container
                const previewContainer = $('<div class="preview-container"></div>').css({
                    "margin-right": "10px",
                    "position": "relative"
                });

                // Create preview image
                const objectUrl = URL.createObjectURL(file);
                const previewImg = $('<span class="d-block border border-secondary preview_img_span me-2 p-1 rounded text-white" style="cursor:pointer; max-width:150px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis" title="'+file.name+'">'+file.name+'</span>').attr('data-img_url', objectUrl).css({
                    "height": "auto",
                    "width": "auto",
                    "border-radius": "4px"
                });

                // Create close button
                const closeButton = $('<button class="close-preview">&times;</button>')
                    .css({
                        "position": "absolute",
                        "top": "-8px",
                        "right": "-5px",
                        "color": "red",
                        "border": "none",
                        "border-radius": "50%",
                        "width": "20px",
                        "height": "20px",
                        "line-height": "1",
                        "padding": "0",
                        "cursor": "pointer"
                    })
                    .data('index', selectedFiles.length - 1);

                // Add event listener to close button
                closeButton.on('click', function() {
                    const indexToRemove = parseInt($(this).data('index'));
                    selectedFiles.splice(indexToRemove, 1);
                    $(this).parent('.preview-container').remove();
                    updateFileInput();

                    $('.comment_attachments .preview-container').each(function(newIndex) {
                        $(this).find('.close-preview').data('index', newIndex);
                    });
                });

                // Append elements
                previewContainer.append(previewImg);
                previewContainer.append(closeButton);
                commentAttachmentsDiv.append(previewContainer);

                // Clean up URL when image loads
                previewImg.on('load', function() {
                    URL.revokeObjectURL(objectUrl);
                });
            }
        });








        async function fetchAdmins() {
            try {

                const fetchAdminsUrl = "{{ route('fetchAdmins') }}";
                const response = await fetch(fetchAdminsUrl);
                const admins = await response.json();
                return admins;
            } catch (error) {
                console.error('Failed to fetch mention users:', error);
                return [];
            }
        }

        fetchAdmins().then(admins => {
            const adminIds = new Set(assignedUsers.map(u => u.id));
            admins.forEach(admin => {
                if (!adminIds.has(admin.id)) {
                    allMentionableUsers.push(admin);
                }
            });
        });







        const handleMentionSelection = function(userId, userName, userEmail) {
            console.log("Handling mention selection for:", userName);

            try {
                const editor = $('#comment-input');

                // First try to use the stored cursor position if available
                let storedRange = editor.data('lastCursorPosition');

                // Get the current selection state
                const originalSelection = window.getSelection();
                let originalRange = null;

                if (originalSelection.rangeCount > 0) {
                    originalRange = originalSelection.getRangeAt(0).cloneRange();
                }

                // Determine which range to use - prefer the stored range if it exists and is in the editor
                let activeRange = null;

                if (storedRange && editor[0].contains(storedRange.commonAncestorContainer)) {
                    activeRange = storedRange;
                    console.log("Using stored cursor position");
                } else if (originalRange && editor[0].contains(originalRange.commonAncestorContainer)) {
                    activeRange = originalRange;
                    console.log("Using current selection position");
                }

                // Verify we actually have a valid selection inside the editor
                const isSelectionInEditor = activeRange !== null;

                // Check if we're in a mention context (typed @something)
                let mentionTextFound = false;
                let mentionStartPosition = -1;
                let mentionEndPosition = -1;
                let mentionText = '';

                if (isSelectionInEditor) {
                    const currentNode = activeRange.startContainer;

                    // Only check for mention text if we're in a text node
                    if (currentNode.nodeType === Node.TEXT_NODE) {
                        const text = currentNode.textContent;
                        const cursorPos = activeRange.startOffset;

                        // Find the start of the current word (looking backwards from cursor)
                        let wordStart = cursorPos;
                        while (wordStart > 0 && !/\s/.test(text[wordStart - 1])) {
                            wordStart--;
                        }

                        // Extract the current word up to cursor position
                        const currentWord = text.substring(wordStart, cursorPos);

                        // If the current word starts with @, we've found our mention text
                        if (currentWord.startsWith('@')) {
                            mentionTextFound = true;
                            mentionStartPosition = wordStart;
                            mentionEndPosition = cursorPos;
                            mentionText = currentWord;
                            console.log("Found mention text:", mentionText);
                        }
                    }
                }

                // Perform a targeted update based on our findings
                if (isSelectionInEditor && mentionTextFound) {
                    // We have a cursor in the editor with mention text - perfect case
                    console.log("Mention text found, replacing with user mention");

                    const startContainer = activeRange.startContainer;

                    // Create the mention span
                    const mentionSpan = document.createElement('span');
                    mentionSpan.className = 'mentioned-user';
                    mentionSpan.textContent = `@${userName}`;
                    mentionSpan.setAttribute('data-user-id', userId);
                    mentionSpan.setAttribute('data-user-name', userName);
                    mentionSpan.setAttribute('data-user-email', userEmail);
                    mentionSpan.style.color = '#ff4c00';
                    mentionSpan.style.fontWeight = 'bold';

                    // Create a range to select the entire mention text (e.g., "@per")
                    const mentionRange = document.createRange();
                    mentionRange.setStart(startContainer, mentionStartPosition);
                    mentionRange.setEnd(startContainer, mentionEndPosition);

                    // Delete the mention text
                    mentionRange.deleteContents();

                    // Insert the mention span
                    mentionRange.insertNode(mentionSpan);

                    // Add space after mention
                    const spaceNode = document.createTextNode('\u00A0');

                    // Create a range after the mention span
                    const afterMentionRange = document.createRange();
                    afterMentionRange.setStartAfter(mentionSpan);
                    afterMentionRange.setEndAfter(mentionSpan);
                    afterMentionRange.insertNode(spaceNode);

                    // Set cursor after the space
                    afterMentionRange.setStartAfter(spaceNode);
                    afterMentionRange.setEndAfter(spaceNode);
                    originalSelection.removeAllRanges();
                    originalSelection.addRange(afterMentionRange);

                } else if (isSelectionInEditor) {
                    // Check if the @ symbol is present at the cursor position
                    let atSymbolFound = false;
                    let atPosition = -1;

                    if (isSelectionInEditor) {
                        const currentNode = activeRange.startContainer;

                        // Only check for @ if we're in a text node
                        if (currentNode.nodeType === Node.TEXT_NODE) {
                            const text = currentNode.textContent;
                            const cursorPos = activeRange.startOffset;

                            // Find @ symbol right before cursor
                            if (cursorPos > 0 && text[cursorPos - 1] === '@') {
                                atSymbolFound = true;
                                atPosition = cursorPos - 1;
                            }
                        }
                    }

                    if (atSymbolFound) {
                        // We have a cursor in the editor with just @ symbol
                        console.log("@ found at cursor position, performing targeted insertion");

                        const startContainer = activeRange.startContainer;
                        const startOffset = activeRange.startOffset;

                        // Create the mention span
                        const mentionSpan = document.createElement('span');
                        mentionSpan.className = 'mentioned-user';
                        mentionSpan.textContent = `@${userName}`;
                        mentionSpan.setAttribute('data-user-id', userId);
                        mentionSpan.setAttribute('data-user-name', userName);
                        mentionSpan.setAttribute('data-user-email', userEmail);
                        mentionSpan.style.color = '#ff4c00';
                        mentionSpan.style.fontWeight = 'bold';

                        // Create a range to select just the @ symbol
                        const atRange = document.createRange();
                        atRange.setStart(startContainer, atPosition);
                        atRange.setEnd(startContainer, startOffset);

                        // Delete the @ symbol
                        atRange.deleteContents();

                        // Insert the mention span
                        atRange.insertNode(mentionSpan);

                        // Add space after mention
                        const spaceNode = document.createTextNode('\u00A0');

                        // Create a range after the mention span
                        const afterMentionRange = document.createRange();
                        afterMentionRange.setStartAfter(mentionSpan);
                        afterMentionRange.setEndAfter(mentionSpan);
                        afterMentionRange.insertNode(spaceNode);

                        // Set cursor after the space
                        afterMentionRange.setStartAfter(spaceNode);
                        afterMentionRange.setEndAfter(spaceNode);
                        originalSelection.removeAllRanges();
                        originalSelection.addRange(afterMentionRange);
                    } else {
                        // We have a cursor in the editor but no @ symbol or mention text
                        console.log("Cursor in editor but no @ symbol, inserting at cursor");

                        // Create the mention span
                        const mentionSpan = document.createElement('span');
                        mentionSpan.className = 'mentioned-user';
                        mentionSpan.textContent = `@${userName}`;
                        mentionSpan.setAttribute('data-user-id', userId);
                        mentionSpan.setAttribute('data-user-name', userName);
                        mentionSpan.setAttribute('data-user-email', userEmail);
                        mentionSpan.style.color = '#ff4c00';
                        mentionSpan.style.fontWeight = 'bold';

                        // Insert at cursor position
                        activeRange.insertNode(mentionSpan);

                        // Add space after mention
                        const spaceNode = document.createTextNode('\u00A0');

                        // Create range after mention span
                        const afterMentionRange = document.createRange();
                        afterMentionRange.setStartAfter(mentionSpan);
                        afterMentionRange.setEndAfter(mentionSpan);
                        afterMentionRange.insertNode(spaceNode);

                        // Set cursor after space
                        afterMentionRange.setStartAfter(spaceNode);
                        afterMentionRange.setEndAfter(spaceNode);
                        originalSelection.removeAllRanges();
                        originalSelection.addRange(afterMentionRange);
                    }
                } else {
                    // Try to restore the cursor position from the stored position
                    if (storedRange) {
                        try {
                            // Set the selection to the stored range
                            originalSelection.removeAllRanges();
                            originalSelection.addRange(storedRange);

                            // Create the mention span
                            const mentionSpan = document.createElement('span');
                            mentionSpan.className = 'mentioned-user';
                            mentionSpan.textContent = `@${userName}`;
                            mentionSpan.setAttribute('data-user-id', userId);
                            mentionSpan.setAttribute('data-user-name', userName);
                            mentionSpan.setAttribute('data-user-email', userEmail);
                            mentionSpan.style.color = '#ff4c00';
                            mentionSpan.style.fontWeight = 'bold';

                            // Insert at the stored cursor position
                            storedRange.insertNode(mentionSpan);

                            // Add space after mention
                            const spaceNode = document.createTextNode('\u00A0');

                            // Create range after mention span
                            const afterMentionRange = document.createRange();
                            afterMentionRange.setStartAfter(mentionSpan);
                            afterMentionRange.setEndAfter(mentionSpan);
                            afterMentionRange.insertNode(spaceNode);

                            // Set cursor after space
                            afterMentionRange.setStartAfter(spaceNode);
                            afterMentionRange.setEndAfter(spaceNode);
                            originalSelection.removeAllRanges();
                            originalSelection.addRange(afterMentionRange);

                            console.log("Inserted at stored cursor position");

                        } catch (rangeError) {
                            console.error("Error restoring stored range:", rangeError);
                            // Fall back to appending at the end
                            appendMentionAtEnd();
                        }
                    } else {
                        // No valid selection in the editor, use direct HTML manipulation
                        appendMentionAtEnd();
                    }
                }

                // Helper function to append mention at the end
                function appendMentionAtEnd() {
                    console.log("No valid selection, appending to end");

                    // Get current editor HTML
                    let editorHtml = editor.html();

                    // Check for trailing @ symbol
                    if (editorHtml.endsWith('@')) {
                        editorHtml = editorHtml.substring(0, editorHtml.length - 1);
                    }

                    // Create mention HTML
                    const mentionHtml =
                        `<span class="mentioned-user" data-user-id="${userId}" data-user-name="${userName}" data-user-email="${userEmail}" style="color: #ff4c00; font-weight: bold;">@${userName}</span>&nbsp;`;

                    // Append to editor
                    editor.html(editorHtml + mentionHtml);

                    // Move cursor to end
                    placeCaretAtEnd(editor[0]);
                }

                // Store mention data in hidden field
                storeMentionData(userId, userName, userEmail);

                // Hide suggestions and reset state
                $('#mention-suggestions').addClass('d-none').hide();
                mentionStart = -1;
                currentMention = '';
                selectedIndex = -1;

                // Update the UI
                updateMentionedUsers();

                // Focus back on editor
                editor.focus();

                // Final cleanup after a short delay
                setTimeout(function() {
                    cleanupMentionHtml(editor);
                }, 10);

                console.log("Final editor content:", editor.html());
            } catch (error) {
                console.error("Error in handleMentionSelection:", error);

                // Ultimate fallback - direct HTML insertion
                try {
                    const editor = $('#comment-input');
                    let editorHtml = editor.html();

                    // Check for trailing @ and remove it
                    if (editorHtml.endsWith('@')) {
                        editorHtml = editorHtml.substring(0, editorHtml.length - 1);
                    }

                    // Create and append the mention
                    const mentionHtml =
                        `<span class="mentioned-user" data-user-id="${userId}" data-user-name="${userName}" data-user-email="${userEmail}" style="color: #ff4c00; font-weight: bold;">@${userName}</span>&nbsp;`;
                    editor.html(editorHtml + mentionHtml);

                    // Place cursor at end and update UI
                    placeCaretAtEnd(editor[0]);
                    $('#mention-suggestions').addClass('d-none').hide();
                    updateMentionedUsers();

                    console.log("Ultimate fallback used, final content:", editor.html());
                } catch (fallbackError) {
                    console.error("Ultimate fallback also failed:", fallbackError);
                }
            }
        };

        // Helper function to store mention data in hidden field
        function storeMentionData(userId, userName, userEmail) {
            // Create or update the hidden input field for mention data
            if (!$('#mention-data').length) {
                $('<input type="hidden" id="mention-data" name="mention_data">').appendTo('form');
            }

            // Get current mentions and add the new one
            const currentMentions = JSON.parse($('#mention-data').val() || '[]');

            // Check if this user ID is already in the mentions data
            const existingMentionIndex = currentMentions.findIndex(mention => mention.userId === userId);
            if (existingMentionIndex === -1) {
                // Only add if not already present
                currentMentions.push({
                    userId: userId,
                    userName: userName,
                    userEmail: userEmail
                });
                $('#mention-data').val(JSON.stringify(currentMentions));
            }
        }

        // Helper function to clean up HTML and fix any mention-related issues
        function cleanupMentionHtml(editor) {
            // Get current HTML
            let html = editor.html();

            // Save selection if possible
            const selection = window.getSelection();
            let savedRange = null;

            if (selection.rangeCount > 0) {
                savedRange = selection.getRangeAt(0).cloneRange();
            }

            // Fix orphaned @ symbols
            html = html.replace(/<br\s*\/?>@(<span class="mentioned-user")/g, '<br>$1');
            html = html.replace(/<br\s*\/?>\s*@(<span class="mentioned-user")/g, '<br>$1');
            html = html.replace(/<div>@(<span class="mentioned-user")/g, '<div>$1');
            html = html.replace(/<div>\s*@(<span class="mentioned-user")/g, '<div>$1');
            html = html.replace(/<\/div>@(<span class="mentioned-user")/g, '</div>$1');
            html = html.replace(/<\/div>\s*@(<span class="mentioned-user")/g, '</div>$1');
            html = html.replace(/<p>@(<span class="mentioned-user")/g, '<p>$1');
            html = html.replace(/<p>\s*@(<span class="mentioned-user")/g, '<p>$1');

            // Handle @ in its own div
            html = html.replace(/<div>@<\/div>$/g, '<div></div>');
            html = html.replace(/<div>\s*@\s*<\/div>$/g, '<div></div>');
            html = html.replace(/<div>@<\/div>(<span class="mentioned-user")/g, '<div>$1');
            html = html.replace(/<div>\s*@\s*<\/div>(<span class="mentioned-user")/g, '<div>$1');

            // Fix orphaned @ symbols at the end of content
            html = html.replace(/\s@\s*$/, ' ');
            html = html.replace(/\s@<div>/, '<div>');
            html = html.replace(/\s@\s*<div>/, '<div>');

            // Remove any <font> tags that might be causing extra @ symbols
            html = html.replace(/<font[^>]*>@<\/font>/g, '');
            html = html.replace(/<font[^>]*><b>@<\/b><\/font>/g, '');

            // Update HTML if changes were made
            if (html !== editor.html()) {
                editor.html(html);

                // Try to restore selection
                if (savedRange) {
                    try {
                        selection.removeAllRanges();
                        selection.addRange(savedRange);
                    } catch (e) {
                        console.error("Could not restore selection:", e);
                    }
                }
            }

            // Standard cleanup
            cleanupRemovedMentions();
        }






        // Helper function to place caret at the end of an element
        function placeCaretAtEnd(el) {
            el.focus();
            if (typeof window.getSelection != "undefined" && typeof document.createRange != "undefined") {
                const range = document.createRange();
                range.selectNodeContents(el);
                range.collapse(false);
                const sel = window.getSelection();
                sel.removeAllRanges();
                sel.addRange(range);
            } else if (typeof document.body.createTextRange != "undefined") {
                const textRange = document.body.createTextRange();
                textRange.moveToElementText(el);
                textRange.collapse(false);
                textRange.select();
            }
        }

        // Helper function to get cursor position in contenteditable
        function getCursorPosition(element) {
            let position = 0;
            const selection = window.getSelection();

            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                const preCaretRange = range.cloneRange();
                preCaretRange.selectNodeContents(element);
                preCaretRange.setEnd(range.startContainer, range.startOffset);
                position = preCaretRange.toString().length;
            }

            return position;
        }

        // Helper function to find text node at a given position
        function findTextNodeAtPosition(root, position) {
            const treeWalker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, null, false);
            let currentNode = treeWalker.nextNode();
            let currentPosition = 0;

            while (currentNode) {
                const nodeLength = currentNode.nodeValue.length;

                if (position >= currentPosition && position < currentPosition + nodeLength) {
                    return currentNode;
                }

                currentPosition += nodeLength;
                currentNode = treeWalker.nextNode();
            }

            return null;
        }

        // Helper function to get node offset in parent
        function getNodeOffset(parent, node) {
            let offset = 0;
            const treeWalker = document.createTreeWalker(parent, NodeFilter.SHOW_TEXT, null, false);
            let currentNode = treeWalker.nextNode();

            while (currentNode && currentNode !== node) {
                offset += currentNode.nodeValue.length;
                currentNode = treeWalker.nextNode();
            }

            return offset;
        }

        // Function to update selection in dropdown
        const updateSelection = function() {
            $('#mention-suggestions li').removeClass('selected');
            if (selectedIndex !== -1) {
                $('#mention-suggestions li').eq(selectedIndex).addClass('selected');
            }
        };



        // Helper function to show mention suggestions
        function showMentionSuggestions(editor, selection) {
            try {
                // Get cursor position
                let cursorRect = null;
                if (selection && selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    cursorRect = range.getBoundingClientRect();
                }

                // Show all users in the mention suggestions
                const suggestions = $('#mention-suggestions ul');
                suggestions.empty();

                // Make sure we have the latest state of mentioned users
                updateMentionedUsers();

                console.log("Showing mention suggestions, current selectedUsers:", Array.from(selectedUsers));

                // Show all mentionable users, regardless of whether they've been mentioned before
                allMentionableUsers.forEach(user => {
                    const profileImageUrl = user.profile_image ? `/storage/images/${user.profile_image}` :
                        '/images/default-user.jpg';
                    const li = $('<li class="mention-item"></li>')
                        .html(`
                        <div class="mention-item-content">
                            <img class="mention-item-avatar" src="${profileImageUrl}" alt="${user.name}" />
                            <span>${user.name}</span>
                        </div>`)
                        .data('user-id', user.id)
                        .data('user-name', user.name)
                        .data('user-email', user.email);
                    suggestions.append(li);
                });

                // Position the suggestions
                const mentionSuggestions = $('#mention-suggestions');
                const editorContainer = editor.closest('.rich-editor-container');
                const editorContainerRect = editorContainer[0].getBoundingClientRect();

                if (cursorRect && cursorRect.bottom > 0) {
                    // Position below the cursor relative to the editor container
                    const top = cursorRect.bottom - editorContainerRect.top + 5;
                    const left = cursorRect.left - editorContainerRect.left;

                    mentionSuggestions.css({
                        'position': 'absolute',
                        'top': top + 'px',
                        'left': left + 'px',
                        'width': '300px',
                        'z-index': '1000'
                    }).removeClass('d-none').show();
                } else {
                    // Fallback to positioning at the top of the editor
                    mentionSuggestions.css({
                        'position': 'absolute',
                        'top': '40px', // Position below the formatting toolbar
                        'left': '10px',
                        'width': '300px',
                        'z-index': '1000'
                    }).removeClass('d-none').show();
                }

                selectedIndex = -1;

                // Trigger the input event to update the mention context
                editor.trigger('input');
            } catch (error) {
                console.error("Show mention suggestions failed:", error);
            }
        }


        // Handle @mentions in contenteditable
        const inputHandler = function(e) {
            // Get the current editor content
            const editorHtml = $(this).html();
            const editorText = $(this).text();

            // Store the current selection position
            const selection = window.getSelection();
            let range = null;
            let selectionOffset = 0;

            if (selection.rangeCount > 0) {
                range = selection.getRangeAt(0).cloneRange();
                selectionOffset = range.startOffset;
            }

            // Skip cleanup if we're in the middle of applying formatting
            if (!window.isApplyingFormatting) {
                // Check for the specific pattern in the example
                const divAtPattern = /<div>@<\/div><span class="mentioned-user"/g;
                if (editorHtml.match(divAtPattern)) {
                    console.log(
                        "Found problematic pattern in input handler: <div>@</div><span class=\"mentioned-user\"");
                    editorHtml = editorHtml.replace(/<div>@<\/div>(<span class="mentioned-user")/g, '<div>$1');
                    editorHtml = editorHtml.replace(/<div>\s*@\s*<\/div>(<span class="mentioned-user")/g, '<div>$1');
                    $(this).html(editorHtml);
                }
                cleanupRemovedMentions();
            }

            // Check if we need to handle mentions
            // Check if the user just typed @ character at any position
            if (e.originalEvent && e.originalEvent.data === '@') {
                // User just typed @ character, show mention suggestions
                handleMentionInput();

                // Check if we just created a new line and typed @
                if ($(this).data('newLineCreated')) {
                    // Set a flag to indicate that this is a new line mention
                    $(this).data('isNewLineMention', true);
                    // Reset the new line created flag
                    $(this).data('newLineCreated', false);
                }
            } else if (editorHtml.endsWith('@')) {
                // If the HTML ends with @, we're starting a new mention
                handleMentionInput();

                // Check if we just created a new line and typed @
                if ($(this).data('newLineCreated')) {
                    // Set a flag to indicate that this is a new line mention
                    $(this).data('isNewLineMention', true);
                    // Reset the new line created flag
                    $(this).data('newLineCreated', false);
                }
            } else if (editorText.includes('@')) {
                // If the text includes @, we might be in the middle of typing a mention
                // Check if the current word at cursor position starts with @
                if (selection && selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const container = range.startContainer;

                    if (container.nodeType === Node.TEXT_NODE) {
                        const text = container.textContent;
                        const cursorPos = range.startOffset;

                        // Find the start of the current word
                        let wordStart = cursorPos;
                        while (wordStart > 0 && !/\s/.test(text[wordStart - 1])) {
                            wordStart--;
                        }

                        // Extract the current word
                        const currentWord = text.substring(wordStart, cursorPos);

                        // If the current word starts with @, we're in a mention context
                        if (currentWord.startsWith('@')) {
                            handleMentionInput();
                            return;
                        }
                    }
                }

                // Check if the last word starts with @ (fallback)
                const words = editorText.split(/\s+/);
                const lastWord = words[words.length - 1];

                if (lastWord.startsWith('@')) {
                    handleMentionInput();
                } else {
                    // If we're not in a mention context, hide the suggestions
                    $('#mention-suggestions').addClass('d-none').hide();

                    // Additional cleanup for any leftover formatting, but only if we're not applying formatting
                    if (!window.isApplyingFormatting) {
                        cleanupRemovedMentions();
                    }
                }
            } else {
                // If we're not in a mention context, hide the suggestions
                $('#mention-suggestions').addClass('d-none').hide();

                // Additional cleanup for any leftover formatting, but only if we're not applying formatting
                if (!window.isApplyingFormatting) {
                    cleanupRemovedMentions();
                }
            }

            // With our simplified approach, we don't need to update format button states here
        };

        // Store the input handler so we can temporarily remove it during cleanup
        $('#comment-input').data('inputHandler', inputHandler);

        // Attach the input handler
        $('#comment-input').on('input', inputHandler);

        // Also handle keydown events to catch delete, backspace, and enter keys
        $('#comment-input').on('keydown', function(e) {
            // If the user is deleting content (Backspace or Delete keys)
            if (e.key === 'Backspace' || e.key === 'Delete') {
                // Save the current selection and cursor position before deletion
                const selection = window.getSelection();
                let savedRange = null;
                let savedNode = null;
                let savedOffset = 0;
                let savedParentNode = null;

                // Check if there's a selection (text is selected)
                const hasSelection = selection.rangeCount > 0 && !selection.isCollapsed;

                if (selection.rangeCount > 0) {
                    savedRange = selection.getRangeAt(0).cloneRange();
                    savedNode = savedRange.startContainer;
                    savedOffset = savedRange.startOffset;
                    savedParentNode = savedNode.parentNode;

                    // Store this position for later use
                    $(this).data('lastCursorPosition', savedRange);
                    $(this).data('deletionCursorNode', savedNode);
                    $(this).data('deletionCursorOffset', savedOffset);
                    $(this).data('deletionCursorParent', savedParentNode);
                    $(this).data('hasSelection', hasSelection);

                    // Also store the current HTML content before deletion
                    $(this).data('preDeleteHtml', $(this).html());
                }

                // Check if we're about to delete a mentioned user
                let isDeletingMention = false;
                let mentionToDelete = null;
                let mentionsInSelection = [];

                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);

                    // If there's a text selection, check if it contains any mention spans
                    if (hasSelection) {
                        // Get all mentioned users in the selection
                        const selectionContents = range.cloneContents();
                        const mentionSpans = selectionContents.querySelectorAll('.mentioned-user');

                        if (mentionSpans.length > 0) {
                            // We have mentions in the selection
                            isDeletingMention = true;
                            mentionsInSelection = Array.from(mentionSpans);

                            // Create a marker at the start of the selection
                            const marker = document.createElement('span');
                            marker.id = 'deletion-marker';
                            marker.style.display = 'none';

                            // Insert marker at the start of the selection
                            const startRange = range.cloneRange();
                            startRange.collapse(true);
                            startRange.insertNode(marker);

                            // Store the marker's parent and position
                            $(this).data('markerParent', marker.parentNode);
                            $(this).data('markerPosition', Array.from(marker.parentNode.childNodes).indexOf(
                                marker));

                            // Let the default deletion happen for selections
                            return;
                        }
                    }

                    // Check if we're at the beginning of a text node after a mention span (for backspace)
                    if (range.startOffset === 0 && e.key === 'Backspace') {
                        // Check if the previous sibling is a mention span
                        let prevSibling = range.startContainer.previousSibling;

                        // If we're in a div or other container, we need to check the last child of the previous sibling
                        if (!prevSibling && range.startContainer.parentNode && range.startContainer.parentNode
                            .previousSibling) {
                            const prevParentSibling = range.startContainer.parentNode.previousSibling;
                            if (prevParentSibling.nodeType === Node.ELEMENT_NODE) {
                                // Get the last child of the previous sibling
                                if (prevParentSibling.lastChild) {
                                    prevSibling = prevParentSibling.lastChild;
                                }
                            }
                        }

                        if (prevSibling && prevSibling.nodeType === Node.ELEMENT_NODE &&
                            prevSibling.classList && prevSibling.classList.contains('mentioned-user')) {
                            isDeletingMention = true;
                            mentionToDelete = prevSibling;

                            // Create a marker at the current position
                            const marker = document.createElement('span');
                            marker.id = 'deletion-marker';
                            marker.style.display = 'none';
                            range.insertNode(marker);

                            // Store the marker's parent and position
                            $(this).data('markerParent', marker.parentNode);
                            $(this).data('markerPosition', Array.from(marker.parentNode.childNodes).indexOf(
                                marker));
                        }
                    }

                    // Check if we're right before a mention span with Delete key
                    if (e.key === 'Delete') {
                        // Check if the next sibling is a mention span
                        let nextSibling = range.startContainer.nextSibling;

                        // If we're at the end of a text node
                        if (range.startContainer.nodeType === Node.TEXT_NODE &&
                            range.startOffset === range.startContainer.textContent.length) {

                            // If there's no direct next sibling but we're in a container
                            if (!nextSibling && range.startContainer.parentNode && range.startContainer.parentNode
                                .nextSibling) {
                                const nextParentSibling = range.startContainer.parentNode.nextSibling;
                                if (nextParentSibling.nodeType === Node.ELEMENT_NODE) {
                                    // Get the first child of the next sibling
                                    if (nextParentSibling.firstChild) {
                                        nextSibling = nextParentSibling.firstChild;
                                    }
                                }
                            }

                            if (nextSibling && nextSibling.nodeType === Node.ELEMENT_NODE &&
                                nextSibling.classList && nextSibling.classList.contains('mentioned-user')) {
                                isDeletingMention = true;
                                mentionToDelete = nextSibling;

                                // Create a marker at the current position
                                const marker = document.createElement('span');
                                marker.id = 'deletion-marker';
                                marker.style.display = 'none';
                                range.insertNode(marker);

                                // Store the marker's parent and position
                                $(this).data('markerParent', marker.parentNode);
                                $(this).data('markerPosition', Array.from(marker.parentNode.childNodes).indexOf(
                                    marker));
                            }
                        }
                    }

                    // Check if we're directly inside a mention span
                    let parentNode = range.startContainer;
                    while (parentNode && parentNode !== this) {
                        if (parentNode.nodeType === Node.ELEMENT_NODE &&
                            parentNode.classList && parentNode.classList.contains('mentioned-user')) {
                            isDeletingMention = true;
                            mentionToDelete = parentNode;

                            // Create a marker before the mention
                            const marker = document.createElement('span');
                            marker.id = 'deletion-marker';
                            marker.style.display = 'none';
                            parentNode.parentNode.insertBefore(marker, parentNode);

                            // Store the marker's parent and position
                            $(this).data('markerParent', marker.parentNode);
                            $(this).data('markerPosition', Array.from(marker.parentNode.childNodes).indexOf(
                                marker));
                            break;
                        }
                        parentNode = parentNode.parentNode;
                    }
                }

                if (isDeletingMention) {
                    // Store that we're deleting a mention
                    $(this).data('isDeletingMention', true);

                    // If we found a mention to delete, let's handle it manually
                    if (mentionToDelete) {
                        // Prevent the default backspace/delete behavior
                        e.preventDefault();

                        // Remove the mention element
                        if (mentionToDelete.parentNode) {
                            mentionToDelete.parentNode.removeChild(mentionToDelete);
                        }

                        // Set cursor position at the marker
                        setTimeout(function() {
                            const editor = $('#comment-input');
                            const marker = editor.find('#deletion-marker')[0];

                            if (marker) {
                                const range = document.createRange();
                                const selection = window.getSelection();

                                // Set position at the marker
                                range.setStartAfter(marker);
                                range.setEndAfter(marker);

                                // Remove the marker
                                marker.parentNode.removeChild(marker);

                                // Apply the selection
                                selection.removeAllRanges();
                                selection.addRange(range);

                                // Clean up any formatting issues
                                cleanupRemovedMentions();
                            } else {
                                // If marker is gone, try to restore using stored parent and position
                                const markerParent = editor.data('markerParent');
                                const markerPosition = editor.data('markerPosition');

                                if (markerParent && typeof markerPosition === 'number') {
                                    try {
                                        const range = document.createRange();
                                        const selection = window.getSelection();

                                        // Find the node at or near the marker position
                                        let targetNode = markerParent;
                                        let targetOffset = Math.min(markerPosition, markerParent.childNodes
                                            .length);

                                        // If there's a child at that position, set position before it
                                        if (targetOffset < markerParent.childNodes.length) {
                                            range.setStartBefore(markerParent.childNodes[targetOffset]);
                                            range.setEndBefore(markerParent.childNodes[targetOffset]);
                                        } else {
                                            // Otherwise set at the end of the parent
                                            range.setStartAfter(markerParent.lastChild || markerParent);
                                            range.setEndAfter(markerParent.lastChild || markerParent);
                                        }

                                        // Apply the selection
                                        selection.removeAllRanges();
                                        selection.addRange(range);

                                        // Clean up any formatting issues
                                        cleanupRemovedMentions();
                                    } catch (error) {
                                        console.error(
                                            "Error restoring cursor using marker parent/position:",
                                            error);
                                    }
                                }
                            }
                        }, 0);

                        return;
                    }
                }

                // For normal backspace/delete (not directly on a mention), we'll clean up after the deletion occurs
                setTimeout(function() {
                    const editor = $('#comment-input');
                    const isDeletingMention = editor.data('isDeletingMention');
                    const hasSelection = editor.data('hasSelection');

                    // Clean up any formatting issues
                    cleanupRemovedMentions();

                    // Now restore the cursor position if we were deleting a mention or had a selection
                    if (isDeletingMention || hasSelection) {
                        try {
                            // Check for the marker first
                            const marker = editor.find('#deletion-marker')[0];
                            if (marker) {
                                const range = document.createRange();
                                const selection = window.getSelection();

                                // Set position at the marker
                                range.setStartAfter(marker);
                                range.setEndAfter(marker);

                                // Remove the marker
                                marker.parentNode.removeChild(marker);

                                // Apply the selection
                                selection.removeAllRanges();
                                selection.addRange(range);

                                // Store this new position for future use
                                editor.data('lastCursorPosition', range.cloneRange());
                            } else {
                                // Try to restore from the saved range
                                const savedRange = editor.data('lastCursorPosition');
                                if (savedRange) {
                                    try {
                                        const selection = window.getSelection();
                                        selection.removeAllRanges();
                                        selection.addRange(savedRange);
                                    } catch (rangeError) {
                                        console.error("Error restoring from saved range:", rangeError);

                                        // Fallback to the saved node and offset
                                        const savedNode = editor.data('deletionCursorNode');
                                        const savedOffset = editor.data('deletionCursorOffset');
                                        const savedParent = editor.data('deletionCursorParent');

                                        if (savedNode && savedNode.parentNode) {
                                            try {
                                                const newRange = document.createRange();
                                                newRange.setStart(savedNode, savedOffset);
                                                newRange.setEnd(savedNode, savedOffset);

                                                selection.removeAllRanges();
                                                selection.addRange(newRange);

                                                // Store this new position for future use
                                                editor.data('lastCursorPosition', newRange.cloneRange());
                                            } catch (nodeError) {
                                                console.error("Error restoring from saved node:",
                                                    nodeError);

                                                // Try to place cursor at a reasonable position, not just the beginning
                                                if (editor[0].childNodes.length > 0) {
                                                    try {
                                                        // Try to find a text node near where we were
                                                        const textNodes = [];
                                                        const walker = document.createTreeWalker(
                                                            editor[0],
                                                            NodeFilter.SHOW_TEXT,
                                                            null,
                                                            false
                                                        );

                                                        let node;
                                                        while (node = walker.nextNode()) {
                                                            textNodes.push(node);
                                                        }

                                                        if (textNodes.length > 0) {
                                                            // Find a suitable text node - prefer one in the middle rather than at the beginning
                                                            const targetNode = textNodes[Math.min(textNodes
                                                                .length - 1, 1)];
                                                            const fallbackRange = document.createRange();
                                                            fallbackRange.setStart(targetNode, 0);
                                                            fallbackRange.setEnd(targetNode, 0);

                                                            selection.removeAllRanges();
                                                            selection.addRange(fallbackRange);

                                                            // Store this new position for future use
                                                            editor.data('lastCursorPosition', fallbackRange
                                                                .cloneRange());
                                                        } else {
                                                            // If no text nodes, place at the end of the editor
                                                            const fallbackRange = document.createRange();
                                                            fallbackRange.selectNodeContents(editor[0]);
                                                            fallbackRange.collapse(
                                                                false); // collapse to end

                                                            selection.removeAllRanges();
                                                            selection.addRange(fallbackRange);

                                                            // Store this new position for future use
                                                            editor.data('lastCursorPosition', fallbackRange
                                                                .cloneRange());
                                                        }
                                                    } catch (fallbackError) {
                                                        console.error(
                                                            "Error with fallback cursor positioning:",
                                                            fallbackError);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("Error restoring cursor position after deletion:", error);
                        }

                        // Reset the flags
                        editor.removeData('isDeletingMention');
                        editor.removeData('hasSelection');
                    }
                }, 0);
            }

            // If the user presses Enter, we need to handle the case where they might type @ on a new line
            if (e.key === 'Enter') {
                // Set a flag to indicate that we're starting a new line
                $(this).data('newLineCreated', true);

                // Clean up after the new line is created
                setTimeout(function() {
                    // Check for the specific pattern in the example
                    const editor = $('#comment-input');
                    let editorHtml = editor.html();

                    // Check for the specific pattern in the example
                    const divAtPattern = /<div>@<\/div><span class="mentioned-user"/g;
                    if (editorHtml.match(divAtPattern)) {
                        console.log(
                            "Found problematic pattern after Enter: <div>@</div><span class=\"mentioned-user\""
                        );
                        editorHtml = editorHtml.replace(/<div>@<\/div>(<span class="mentioned-user")/g,
                            '<div>$1');
                        editorHtml = editorHtml.replace(
                            /<div>\s*@\s*<\/div>(<span class="mentioned-user")/g, '<div>$1');
                        editor.html(editorHtml);
                    }

                    cleanupRemovedMentions();
                }, 0);
            }
        });

        // Handle focus and click events to ensure cleanup happens when the user interacts with the editor
        $('#comment-input').on('focus click', function() {
            // Clean up any leftover formatting when the user focuses or clicks in the editor
            cleanupRemovedMentions();
        });

        // Handle paste events to clean up any formatting that might be pasted into the editor
        $('#comment-input').on('paste', function(e) {
            // We'll clean up after the paste occurs
            setTimeout(function() {
                cleanupRemovedMentions();
            }, 0);
        });

        // Add a mutation observer to detect changes to the DOM and clean up formatting

        const commentInput = document.getElementById('comment-input');
        if (commentInput) {
            const observer = new MutationObserver(function(mutations) {
                // Clean up any leftover formatting when the DOM changes
                cleanupRemovedMentions();
            });

            // Observe changes to the comment input's content
            observer.observe(commentInput, {
                childList: true,
                subtree: true,
                characterData: true,
                attributes: true,
                attributeFilter: ['style', 'class']
            });
        }


        // Instead, we'll use resetFormatButtonStates to clear button states
        function resetFormatButtonStates() {
            $('.format-btn[data-format="bold"]').removeClass('active');
            $('.format-btn[data-format="italic"]').removeClass('active');
            $('.format-btn[data-format="underline"]').removeClass('active');
        }



        // Simplified helper function to apply formatting
        function applyFormatting(format) {
            const editor = $('#comment-input');
            const selection = window.getSelection();

            // Focus the editor first
            editor.focus();

            // Only apply formatting if there's a selection
            if (selection.rangeCount > 0 && !selection.isCollapsed) {
                // Check if the selection is within the editor
                const range = selection.getRangeAt(0);
                if (editor[0].contains(range.commonAncestorContainer)) {
                    // Set a flag to prevent the input handler from triggering cleanup
                    window.isApplyingFormatting = true;

                    // Apply the formatting command
                    document.execCommand(format, false, null);

                    // Reset the flag after a short delay
                    setTimeout(function() {
                        window.isApplyingFormatting = false;
                    }, 0);

                    // Reset format button states
                    resetFormatButtonStates();

                    // Toggle active state on the button for the applied format
                    $(`.format-btn[data-format="${format}"]`).toggleClass('active');
                }
            }

            // Focus back on the editor
            editor.focus();
        }

        // Format buttons click handlers for the main comment input
        $('.formatting-toolbar .format-btn').on('click', function() {
            const format = $(this).data('format');
            applyFormatting(format);
        });

        // Also add keyboard shortcuts for formatting
        $('#comment-input').on('keydown', function(e) {
            const selection = window.getSelection();

            // Ctrl+@ or Ctrl+Shift+2 for mention (common keyboard shortcut for @)
            if (e.ctrlKey && (e.key === '@' || (e.shiftKey && e.key === '2'))) {
                e.preventDefault();
                insertMentionAtCursor();
                return;
            }

            // Only apply formatting if there's a selection
            if (selection.rangeCount > 0 && !selection.isCollapsed) {
                // Ctrl+B for bold
                if (e.ctrlKey && e.key === 'b') {
                    e.preventDefault();
                    applyFormatting('bold');
                }
                // Ctrl+I for italic
                else if (e.ctrlKey && e.key === 'i') {
                    e.preventDefault();
                    applyFormatting('italic');
                }
                // Ctrl+U for underline
                else if (e.ctrlKey && e.key === 'u') {
                    e.preventDefault();
                    applyFormatting('underline');
                }
            }
        });

        // Function to insert mention at cursor position
        function insertMentionAtCursor() {
            // Get the editor
            const editor = $('#comment-input');

            // Focus the editor
            editor.focus();

            // Check if we already have an @ symbol at the cursor
            const selection = window.getSelection();
            let shouldInsertAtSymbol = true;

            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);

                // Check if we're inside the editor
                if (editor[0].contains(range.commonAncestorContainer)) {
                    // Check if we're already after an @ symbol
                    const currentNode = range.startContainer;
                    if (currentNode.nodeType === Node.TEXT_NODE) {
                        const text = currentNode.textContent;
                        const cursorPos = range.startOffset;

                        // Check if the character before the cursor is @
                        if (cursorPos > 0 && text[cursorPos - 1] === '@') {
                            shouldInsertAtSymbol = false;
                        }
                    }

                    // Only insert @ if needed
                    if (shouldInsertAtSymbol) {
                        // Insert @ at the current position
                        const atSymbol = document.createTextNode('@');
                        range.insertNode(atSymbol);

                        // Move cursor after the @ symbol
                        range.setStartAfter(atSymbol);
                        range.setEndAfter(atSymbol);
                        selection.removeAllRanges();
                        selection.addRange(range);

                        // Store the cursor position for later use
                        editor.data('lastCursorPosition', range.cloneRange());
                    }

                    // Get the updated cursor position for accurate positioning
                    const updatedRange = selection.getRangeAt(0);
                    const cursorRect = updatedRange.getBoundingClientRect();

                    // Show suggestions directly
                    showAllMentionSuggestions(editor, selection);

                    // Set a flag to indicate we're in a mention context
                    editor.data('inMentionContext', true);

                    // Trigger the input event to update the mention context
                    editor.trigger('input');
                } else {
                    // If we're not in the editor, try to find a better position
                    // First check if we have a stored cursor position
                    const storedRange = editor.data('lastCursorPosition');

                    if (storedRange && editor[0].contains(storedRange.commonAncestorContainer)) {
                        try {
                            // Restore the stored cursor position
                            selection.removeAllRanges();
                            selection.addRange(storedRange);

                            // Insert @ at the stored position
                            const atSymbol = document.createTextNode('@');
                            storedRange.insertNode(atSymbol);

                            // Move cursor after the @ symbol
                            const newRange = document.createRange();
                            newRange.setStartAfter(atSymbol);
                            newRange.setEndAfter(atSymbol);
                            selection.removeAllRanges();
                            selection.addRange(newRange);

                            // Store the updated cursor position
                            editor.data('lastCursorPosition', newRange.cloneRange());

                            // Show suggestions
                            showAllMentionSuggestions(editor, selection);

                            // Set a flag to indicate we're in a mention context
                            editor.data('inMentionContext', true);

                            // Trigger the input event to update the mention context
                            editor.trigger('input');

                            console.log("Inserted @ at stored cursor position");
                            return;
                        } catch (error) {
                            console.error("Error restoring stored cursor position:", error);
                            // Fall back to placing cursor at the end
                        }
                    }

                    // If we couldn't use the stored position, place cursor at the end
                    placeCaretAtEnd(editor[0]);

                    // Get the current HTML
                    const editorHtml = editor.html();

                    // Check if the editor already ends with @
                    if (!editorHtml.endsWith('@')) {
                        // Append @ symbol
                        editor.html(editorHtml + '@');
                    }

                    // Place cursor at the end
                    placeCaretAtEnd(editor[0]);

                    // Show suggestions
                    showAllMentionSuggestions(editor, window.getSelection());
                }
            } else {
                // If there's no selection, check if we have a stored cursor position
                const storedRange = editor.data('lastCursorPosition');

                if (storedRange && editor[0].contains(storedRange.commonAncestorContainer)) {
                    try {
                        // Restore the stored cursor position
                        selection.removeAllRanges();
                        selection.addRange(storedRange);

                        // Insert @ at the stored position
                        const atSymbol = document.createTextNode('@');
                        storedRange.insertNode(atSymbol);

                        // Move cursor after the @ symbol
                        const newRange = document.createRange();
                        newRange.setStartAfter(atSymbol);
                        newRange.setEndAfter(atSymbol);
                        selection.removeAllRanges();
                        selection.addRange(newRange);

                        // Store the updated cursor position
                        editor.data('lastCursorPosition', newRange.cloneRange());

                        // Show suggestions
                        showAllMentionSuggestions(editor, selection);

                        // Set a flag to indicate we're in a mention context
                        editor.data('inMentionContext', true);

                        // Trigger the input event to update the mention context
                        editor.trigger('input');

                        console.log("Inserted @ at stored cursor position (no selection)");
                        return;
                    } catch (error) {
                        console.error("Error restoring stored cursor position (no selection):", error);
                        // Fall back to placing cursor at the end
                    }
                }

                // If we couldn't use the stored position, place cursor at the end
                placeCaretAtEnd(editor[0]);

                // Get the current HTML
                const editorHtml = editor.html();

                // Check if the editor already ends with @
                if (!editorHtml.endsWith('@')) {
                    // Append @ symbol
                    editor.html(editorHtml + '@');
                }

                // Place cursor at the end
                placeCaretAtEnd(editor[0]);

                // Show suggestions
                showAllMentionSuggestions(editor, window.getSelection());
            }

            // Clean up any duplicate @ symbols
            setTimeout(function() {
                cleanupRemovedMentions();
            }, 0);
        }

        // Also handle clicks on the @ button
        $('.format-btn[data-format="mention"]').on('click', function() {
            insertMentionAtCursor();
        });

        function handleMentionInput() {
            console.log("Handling mention input");

            try {
                // Get the current editor content and text
                const editor = $('#comment-input');
                const editorHtml = editor.html();
                const editorText = editor.text();

                // Get current selection and cursor position
                const selection = window.getSelection();
                let inMentionContext = false;
                let filterText = '';

                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const cursorPos = range.startOffset;

                    // Get text before cursor to check for @ symbol
                    const container = range.startContainer;
                    let textBeforeCursor = '';

                    if (container.nodeType === Node.TEXT_NODE) {
                        textBeforeCursor = container.textContent.substring(0, cursorPos);

                        // Check if we're right after an @ symbol
                        if (textBeforeCursor.endsWith('@')) {
                            inMentionContext = true;
                            filterText = '';
                        } else {
                            // Check if we're in the middle of typing a mention
                            const lastAtPos = textBeforeCursor.lastIndexOf('@');
                            if (lastAtPos !== -1) {
                                const textAfterAt = textBeforeCursor.substring(lastAtPos + 1);
                                // If there's no space after @, we're in a mention context
                                if (!textAfterAt.includes(' ') && !textAfterAt.includes('\n')) {
                                    inMentionContext = true;
                                    filterText = textAfterAt;
                                }
                            }
                        }
                    } else if (container.nodeType === Node.ELEMENT_NODE) {
                        // If we're in an element node, we need to check if we're inside the editor
                        if (editor[0].contains(container)) {
                            // We're inside the editor, so check if we just typed @
                            const previousNode = range.startContainer.previousSibling;
                            if (previousNode && previousNode.nodeType === Node.TEXT_NODE && previousNode.textContent
                                .endsWith('@')) {
                                inMentionContext = true;
                                filterText = '';
                            }
                        }
                    }
                }

                // If we're not in mention context from cursor position, check if @ is at the end
                if (!inMentionContext && editorHtml.endsWith('@')) {
                    console.log("@ detected at end of HTML");
                    inMentionContext = true;
                    filterText = '';
                }

                // If we're still not in mention context, check the last word
                if (!inMentionContext) {
                    const words = editorText.split(/\s+/);
                    const lastWord = words[words.length - 1];

                    console.log("Last word:", lastWord);

                    if (lastWord.startsWith('@')) {
                        inMentionContext = true;
                        filterText = lastWord.substring(1);
                        console.log("Filtering by:", filterText);
                    }
                }

                // If we're still not in mention context, check if we just typed @ at any position
                if (!inMentionContext && selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const container = range.startContainer;

                    if (container.nodeType === Node.TEXT_NODE) {
                        const text = container.textContent;
                        const cursorPos = range.startOffset;

                        // Check if the character at cursor position is @
                        if (cursorPos > 0 && text[cursorPos - 1] === '@') {
                            inMentionContext = true;
                            filterText = '';
                        }

                        // If not, find the start of the current word
                        if (!inMentionContext) {
                            let wordStart = cursorPos;
                            while (wordStart > 0 && !/\s/.test(text[wordStart - 1])) {
                                wordStart--;
                            }

                            // Extract the current word
                            const currentWord = text.substring(wordStart, cursorPos);

                            // If the current word starts with @, we're in a mention context
                            if (currentWord.startsWith('@')) {
                                inMentionContext = true;
                                filterText = currentWord.substring(1);
                            }
                        }
                    }
                }

                // If we're in a mention context, show the suggestions
                if (inMentionContext) {
                    // Show users based on filter
                    const suggestions = $('#mention-suggestions ul');
                    suggestions.empty();

                    // Make sure we have the latest state of mentioned users
                    updateMentionedUsers();

                    console.log("Handling mention input, current selectedUsers:", Array.from(selectedUsers));

                    // Filter users if needed, but show all available users regardless of whether they've been mentioned before
                    let usersToShow = allMentionableUsers;
                    if (filterText) {
                        usersToShow = allMentionableUsers.filter(user =>
                            user.name.toLowerCase().includes(filterText.toLowerCase())
                        );
                    }

                    // Show the suggestions
                    if (usersToShow.length > 0) {
                        usersToShow.forEach(user => {
                            const profileImageUrl = user.profile_image ? `/storage/images/${user.profile_image}` :
                                '/images/default-user.jpg';
                            const li = $('<li class="mention-item"></li>')
                                .html(`
                                <div class="mention-item-content">
                                    <img class="mention-item-avatar" src="${profileImageUrl}" alt="${user.name}" />
                                    <span>${user.name}</span>
                                </div>`)
                                .data('user-id', user.id)
                                .data('user-name', user.name)
                                .data('user-email', user.email);
                            suggestions.append(li);
                        });

                        // Position the suggestions dropdown
                        const mentionSuggestions = $('#mention-suggestions');
                        const editorContainer = editor.closest('.rich-editor-container');
                        const editorContainerRect = editorContainer[0].getBoundingClientRect();

                        // Get cursor position for positioning
                        let cursorRect = null;
                        if (selection && selection.rangeCount > 0) {
                            const range = selection.getRangeAt(0);

                            // Check if the range is within the editor
                            if (editor[0].contains(range.commonAncestorContainer)) {
                                // Store the current cursor position for later use
                                editor.data('lastCursorPosition', range.cloneRange());

                                // Get the bounding rectangle of the cursor position
                                cursorRect = range.getBoundingClientRect();

                                // If the cursor rect has no dimensions (which can happen),
                                // create a temporary element to get the position
                                if (cursorRect.height === 0 && cursorRect.width === 0) {
                                    // Create a temporary span to get the position
                                    const tempSpan = document.createElement('span');
                                    tempSpan.textContent = '\u200B'; // Zero-width space

                                    // Clone the range to avoid modifying the selection
                                    const tempRange = range.cloneRange();

                                    // Insert at cursor position
                                    tempRange.insertNode(tempSpan);

                                    // Get position of the temporary span
                                    cursorRect = tempSpan.getBoundingClientRect();

                                    // Remove the temporary span
                                    tempSpan.parentNode.removeChild(tempSpan);
                                }
                            }
                        }

                        // Position the suggestions dropdown
                        positionMentionSuggestions(editor, selection, cursorRect);

                        // Make sure the suggestions are visible
                        $('#mention-suggestions').removeClass('d-none').show();
                    } else {
                        $('#mention-suggestions').addClass('d-none').hide();
                    }
                } else {
                    // If we're not in a mention context, hide the suggestions
                    $('#mention-suggestions').addClass('d-none').hide();
                }
            } catch (error) {
                console.error("Error in handleMentionInput:", error);
            }
        }

        // Helper function to show all mention suggestions
        function showAllMentionSuggestions(editor, selection) {
            try {
                console.log("Showing all mention suggestions");

                // Get cursor position
                let cursorRect = null;
                if (selection && selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    if (editor[0].contains(range.commonAncestorContainer)) {
                        // Store the current cursor position for later use
                        editor.data('lastCursorPosition', range.cloneRange());

                        // Get the bounding rectangle of the cursor position
                        cursorRect = range.getBoundingClientRect();

                        // If the cursor rect has no dimensions (which can happen),
                        // create a temporary element to get the position
                        if (cursorRect.height === 0 && cursorRect.width === 0) {
                            try {
                                // Create a temporary span to get the position
                                const tempSpan = document.createElement('span');
                                tempSpan.textContent = '\u200B'; // Zero-width space

                                // Clone the range to avoid modifying the selection
                                const tempRange = range.cloneRange();

                                // Insert at cursor position
                                tempRange.insertNode(tempSpan);

                                // Get position of the temporary span
                                cursorRect = tempSpan.getBoundingClientRect();

                                // Remove the temporary span
                                tempSpan.parentNode.removeChild(tempSpan);
                            } catch (tempSpanError) {
                                console.error("Error creating temporary span:", tempSpanError);
                            }
                        }
                    }
                }

                // Show all users in the mention suggestions
                const suggestions = $('#mention-suggestions ul');
                suggestions.empty();

                // Reset selectedUsers to ensure all users are shown
                selectedUsers = new Set();

                console.log("Showing all users for mention");

                // Show all mentionable users regardless of whether they've been mentioned before
                if (allMentionableUsers && allMentionableUsers.length > 0) {
                    allMentionableUsers.forEach(user => {
                        const profileImageUrl = user.profile_image ? `/storage/images/${user.profile_image}` :
                            '/images/default-user.jpg';
                        const li = $('<li class="mention-item"></li>')
                            .html(`
                            <div class="mention-item-content">
                                <img class="mention-item-avatar" src="${profileImageUrl}" alt="${user.name}" />
                                <span>${user.name}</span>
                            </div>`)
                            .data('user-id', user.id)
                            .data('user-name', user.name)
                            .data('user-email', user.email);
                        suggestions.append(li);
                    });

                    // Position the suggestions
                    positionMentionSuggestions(editor, selection, cursorRect);

                    // Make sure the suggestions are visible
                    $('#mention-suggestions').removeClass('d-none').show();

                    // Set a flag to indicate we're in a mention context
                    editor.data('inMentionContext', true);

                    // Add click event handler for mention items
                    $('.mention-item').off('click').on('click', function() {
                        const userId = $(this).data('user-id');
                        const userName = $(this).data('user-name');
                        const userEmail = $(this).data('user-email');

                        // Call handleMentionSelection with the user data
                        handleMentionSelection(userId, userName, userEmail);
                    });
                } else {
                    console.error("No mentionable users available");
                }
            } catch (error) {
                console.error("Error showing all mention suggestions:", error);

                // Ultimate fallback if an error occurs
                try {
                    const editorRect = editor[0].getBoundingClientRect();
                    $('#mention-suggestions').css({
                        'position': 'fixed',
                        'top': (editorRect.bottom + 5) + 'px',
                        'left': editorRect.left + 'px',
                        'width': '300px',
                        'max-height': '200px',
                        'overflow-y': 'auto',
                        'z-index': '9999'
                    }).removeClass('d-none').show();
                } catch (fallbackError) {
                    console.error("Ultimate fallback positioning also failed:", fallbackError);
                }
            }
        }

        // Helper function to position the mention suggestions
        function positionMentionSuggestions(editor, selection, cursorRect) {
            try {
                const mentionSuggestions = $('#mention-suggestions');
                const editorRect = editor[0].getBoundingClientRect();

                if (cursorRect && cursorRect.bottom > 0) {
                    // Calculate position relative to the viewport
                    const viewportTop = cursorRect.bottom;
                    const viewportLeft = cursorRect.left;

                    // Check if the dropdown would go below the visible area
                    const dropdownHeight = 200; // Approximate max height of dropdown
                    const visibleBottom = window.innerHeight;
                    const wouldOverflowBottom = (viewportTop + dropdownHeight) > visibleBottom;

                    // If it would overflow, position above the cursor instead
                    const finalTop = wouldOverflowBottom ?
                        (cursorRect.top - dropdownHeight - 10) :
                        viewportTop + 5;

                    // Position the dropdown using fixed positioning relative to the viewport
                    mentionSuggestions.css({
                        'position': 'fixed',
                        'top': finalTop + 'px',
                        'left': viewportLeft + 'px',
                        'width': '300px',
                        'max-height': '200px',
                        'overflow-y': 'auto',
                        'z-index': '9999'
                    }).removeClass('d-none').show();

                    // Store the cursor position for later use
                    if (selection && selection.rangeCount > 0) {
                        editor.data('lastCursorPosition', selection.getRangeAt(0).cloneRange());
                    }
                } else {
                    // Get current cursor position if available
                    if (selection && selection.rangeCount > 0) {
                        const range = selection.getRangeAt(0);
                        if (editor[0].contains(range.commonAncestorContainer)) {
                            // Create a temporary span to get the position
                            const tempSpan = document.createElement('span');
                            tempSpan.textContent = '\u200B'; // Zero-width space

                            // Clone the range to avoid modifying the selection
                            const tempRange = range.cloneRange();

                            // Insert at cursor position
                            tempRange.insertNode(tempSpan);

                            // Get position of the temporary span
                            const spanRect = tempSpan.getBoundingClientRect();

                            // Remove the temporary span
                            tempSpan.parentNode.removeChild(tempSpan);

                            // Position the dropdown using fixed positioning relative to the viewport
                            const viewportTop = spanRect.bottom;
                            const viewportLeft = spanRect.left;

                            // Check if the dropdown would go below the visible area
                            const dropdownHeight = 200; // Approximate max height of dropdown
                            const visibleBottom = window.innerHeight;
                            const wouldOverflowBottom = (viewportTop + dropdownHeight) > visibleBottom;

                            // If it would overflow, position above the cursor instead
                            const finalTop = wouldOverflowBottom ?
                                (spanRect.top - dropdownHeight - 10) :
                                viewportTop + 5;

                            mentionSuggestions.css({
                                'position': 'fixed',
                                'top': finalTop + 'px',
                                'left': viewportLeft + 'px',
                                'width': '300px',
                                'max-height': '200px',
                                'overflow-y': 'auto',
                                'z-index': '9999'
                            }).removeClass('d-none').show();

                            // Store the cursor position for later use
                            editor.data('lastCursorPosition', range.cloneRange());

                            // Exit early since we've positioned the dropdown
                            selectedIndex = -1;
                            return;
                        }
                    }

                    // Fallback to positioning below the editor
                    mentionSuggestions.css({
                        'position': 'fixed',
                        'top': (editorRect.bottom + 5) + 'px',
                        'left': editorRect.left + 'px',
                        'width': '300px',
                        'max-height': '200px',
                        'overflow-y': 'auto',
                        'z-index': '9999'
                    }).removeClass('d-none').show();
                }

                selectedIndex = -1; // Reset selection index
            } catch (error) {
                console.error("Error positioning mention suggestions:", error);

                // Ultimate fallback if an error occurs
                try {
                    const editorRect = editor[0].getBoundingClientRect();
                    $('#mention-suggestions').css({
                        'position': 'fixed',
                        'top': (editorRect.bottom + 5) + 'px',
                        'left': editorRect.left + 'px',
                        'width': '300px',
                        'max-height': '200px',
                        'overflow-y': 'auto',
                        'z-index': '9999'
                    }).removeClass('d-none').show();
                } catch (fallbackError) {
                    console.error("Ultimate fallback positioning also failed:", fallbackError);
                }
            }
        }

        // Handle keyboard navigation
        $('.comment-input').on('keydown', function(e) {
            const suggestions = $('#mention-suggestions li');

            if (suggestions.length > 0) {
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, suggestions.length - 1);
                    updateSelection();
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, 0);
                    updateSelection();
                } else if (e.key === 'Enter' && selectedIndex !== -1) {
                    e.preventDefault();
                    const selectedUser = suggestions.eq(selectedIndex);

                    // Get the editor
                    const editor = $('#comment-input');

                    // Restore the cursor position if available
                    const lastCursorPosition = editor.data('lastCursorPosition');
                    if (lastCursorPosition) {
                        const selection = window.getSelection();
                        selection.removeAllRanges();
                        selection.addRange(lastCursorPosition);
                    }

                    // Call handleMentionSelection directly
                    handleMentionSelection(
                        selectedUser.data('user-id'),
                        selectedUser.data('user-name'),
                        selectedUser.data('user-email')
                    );

                    // Hide the suggestions
                    $('#mention-suggestions').addClass('d-none').hide();

                    // Clean up any duplicate @ symbols
                    setTimeout(function() {
                        cleanupRemovedMentions();
                    }, 0);
                } else if (e.key === 'Escape') {
                    $('#mention-suggestions').addClass('d-none');
                    selectedIndex = -1;
                }
            }
        });

        // Handle suggestion selection
        $(document).on('click', '#mention-suggestions li', function() {
            // Get user data
            const userId = $(this).data('user-id');
            const userName = $(this).data('user-name');
            const userEmail = $(this).data('user-email');

            // Get the editor
            const editor = $('#comment-input');

            // Restore the cursor position if available
            const lastCursorPosition = editor.data('lastCursorPosition');
            if (lastCursorPosition) {
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(lastCursorPosition);
            }

            // Call handleMentionSelection directly
            handleMentionSelection(userId, userName, userEmail);

            // Hide the suggestions
            $('#mention-suggestions').addClass('d-none').hide();

            // Focus back on the editor
            editor.focus();

            // Clean up any duplicate @ symbols
            setTimeout(function() {
                cleanupRemovedMentions();
            }, 0);
        });


        function timeAgo(dateString) {
            // Convert the server timestamp to the user's local timezone
            const now = new Date();
            const date = new Date(dateString);

            // Calculate the time difference in seconds using local time
            const seconds = Math.floor((now - date) / 1000);

            const intervalYear = Math.floor(seconds / 31536000);
            const intervalMonth = Math.floor(seconds / 2592000);
            const intervalDay = Math.floor(seconds / 86400);
            const intervalHour = Math.floor(seconds / 3600);
            const intervalMinute = Math.floor(seconds / 60);

            const optionsSameYear = {
                day: 'numeric',
                month: 'long',
                hour: '2-digit',
                minute: '2-digit'
            };
            const optionsDifferentYear = {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };

            if (intervalDay >= 1) {
                if (date.getFullYear() === now.getFullYear()) {
                    return date.toLocaleDateString(undefined, optionsSameYear);
                } else {
                    return date.toLocaleDateString(undefined, optionsDifferentYear);
                }
            }

            if (intervalHour >= 1) return intervalHour + " hour" + (intervalHour === 1 ? "" : "s") + " ago";
            if (intervalMinute >= 1) return intervalMinute + " minute" + (intervalMinute === 1 ? "" : "s") + " ago";
            return "just now";
        }




        console.log('Setting up Echo listener for task-comments.{{ request()->route('task_id') }}');

        window.Echo.private('task-comments.{{ request()->route('task_id') }}')
            .listen('CommentAdded', (e) => {
                console.log('Received comment event:', e);
                console.log('Current user ID:', {{ Auth::id() }});
                console.log('Comment user ID:', e.user_id);
                console.log('Is current user:', e.user_id === {{ Auth::id() }});

                // Create new comment element
                // Convert both IDs to numbers to ensure proper comparison
                const isCurrentUser = parseInt(e.user_id) === parseInt({{ Auth::id() }});
                const commentRow = $('<div class="comment_row" data-comment_id="' + e.comment.id + '"></div>');
                const commentBody = $('<div class="comment_body scrollbar"></div>');

                let formattedComment = e.comment.comment_body;



                // Standardize HTML content for consistent rendering
                console.log('Processing live comment:', formattedComment);

                // First sanitize the HTML to ensure consistent formatting
                formattedComment = sanitizeHtml(formattedComment);

                // Process mentions if they're not already formatted
                if (!formattedComment.includes('class="mentioned-user"')) {
                    const mentionedNames = allMentionableUsers.map(user => user.name);

                    mentionedNames.forEach(name => {
                        const mentionRegex = new RegExp('@' + name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') +
                            '(?=\\s|$)', 'g');
                        formattedComment = formattedComment.replace(mentionRegex,
                            '<span class="mentioned-user">@' + name + '</span>');
                    });
                }

                // Ensure proper handling of HTML entities
                formattedComment = formattedComment.replace(/&lt;/g, '&amp;lt;').replace(/&gt;/g, '&amp;gt;');

                console.log('Processed live comment:', formattedComment);



                const commentText = $('<div class="comment-text">' + formattedComment + '</div>');
                const commentTimestamp = $('<div class="comment-timestamp">' + timeAgo(e.comment.created_at) +
                    '</div>');

                // Only add options icon if it's the current user's message
                let commentTextAndOptions;
                if (isCurrentUser) {
                    // Use both fa and fas classes to ensure compatibility with different Font Awesome versions
                    const commentOptionsIcon = $(
                        '<i class="fa fas fa-ellipsis-h comment_options_icon" data-comment_id="' + e.comment.id +
                        '" style="color: #ff4c00;"></i>');
                    commentTextAndOptions = $(
                        '<div class="comment_text_and_options d-flex "></div>'); // Note the space after d-flex
                    commentTextAndOptions.append(commentText).append(commentOptionsIcon);

                    const dropdown = $('<div class="dropdown-container d-none" data-comment_id="' + e.comment.id +
                        '"><div><a class="edit-comment dropdown-links" data-comment_id="' + e.comment.id +
                        '">Edit</a></div><div><a class="delete-comment dropdown-links" data-comment_id="' + e
                        .comment.id + '">Delete</a></div></div>');
                    commentBody.append(commentTextAndOptions).append(commentTimestamp).append(dropdown);
                } else {
                    commentTextAndOptions = $(
                        '<div class="comment_text_and_options d-flex "></div>'); // Note the space after d-flex
                    commentTextAndOptions.append(commentText);
                    commentBody.append(commentTextAndOptions).append(commentTimestamp);
                }

                // Handle attachments if they exist
                if (e.comment.commentAttachments && e.comment.commentAttachments.length > 0) {

                    const attachmentContainer = $('<div class="attachment_container scrollbar"></div>')
                        .append('<span class="me-2">Attachments (' + e.comment.commentAttachments.length +
                            '): </span>');

                    e.comment.commentAttachments.forEach(attachment => {


                        // Get file extension to determine if it's an image
                        const fileName = attachment.comment_attachment;
                        const fileExtension = fileName.split('.').pop().toLowerCase();
                        const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(
                            fileExtension);

                        // Create attachment element with appropriate styling
                        const attachmentElement = $('<div class="attachment-item me-2 mb-2"></div>');

                        if (isImage) {
                            // For images, show a thumbnail preview
                            attachmentElement.append(`
                            <div class="attachment-preview">
                                <img src="${attachment.full_path}" alt="${fileName}" class="img-thumbnail" style="height: 50px; width: auto;">
                            </div>
                            <div class="attachment-name text-white">${fileName}</div>
                        `);
                        } else {
                            // For non-images, show an icon based on file type
                            let fileIcon = 'fa-file';
                            if (['pdf'].includes(fileExtension)) fileIcon = 'fa-file-pdf';
                            else if (['doc', 'docx'].includes(fileExtension)) fileIcon = 'fa-file-word';
                            else if (['xls', 'xlsx'].includes(fileExtension)) fileIcon = 'fa-file-excel';
                            else if (['zip', 'rar', '7z'].includes(fileExtension)) fileIcon = 'fa-file-archive';
                            else if (['svg'].includes(fileExtension)) fileIcon = 'fa-file-svg';

                            attachmentElement.append(`
                            <div class="attachment-icon">
                                <i class="fas ${fileIcon} fa-2x"></i>
                            </div>
                            <div class="attachment-name text-white">${fileName}</div>
                        `);
                        }

                        // Make the entire element clickable for preview/download
                        attachmentElement.addClass('preview_img_span')
                            .attr('data-img_url', attachment.full_path)
                            .attr('title', fileName)
                            .css({
                                'cursor': 'pointer',
                                'display': 'inline-block',
                                'border': '1px solid #6c757d',
                                'border-radius': '5px',
                                'padding': '5px',
                                'margin-right': '10px',
                                'text-align': 'center',
                                'max-width': '150px'
                            });

                        attachmentContainer.append(attachmentElement);
                    });

                    attachmentContainer.css({
                        'display': 'flex',
                        'flex-wrap': 'wrap',
                        'align-items': 'center',
                        'margin-top': '10px'
                    });

                    commentBody.append(attachmentContainer);
                }

                commentRow.append('<img src="' + e.user_profile_image +
                    '" height="40px" width="40px" class="comment-img rounded rounded-5 mb-1">');
                commentRow.append(commentBody);

                // Make sure the comment_rows container exists and is visible
                if ($('.comment_rows').length === 0) {
                    // If it doesn't exist, create it
                    const commentRowsContainer = $('<div class="comment_rows text-white d-block"></div>');

                    $('.comment-section').append(commentRowsContainer);
                } else {

                    $('.comment_rows').removeClass('d-none').addClass('d-block');
                    $('.comment_rows').css({
                        'height': 'auto'
                    });
                }


                $('.no_comments_row').addClass('d-none').removeClass('d-block');

                // Add highlight class for new comments
                commentRow.addClass('highlight-comment');

                // Add the comment to the comment_rows container with a small delay to ensure proper rendering
                setTimeout(function() {
                    // Append to the bottom instead of prepending to the top
                    $('.comment_rows').append(commentRow);

                    // Debug: Check if the comment options icon is in the DOM
                    console.log('Comment options icon count:', $('.comment_options_icon[data-comment_id="' + e
                        .comment.id + '"]').length);
                    console.log('Comment row HTML:', commentRow.html());

                    // Force re-render of the comment options icon if it's not visible
                    if (isCurrentUser) {
                        const iconExists = $('.comment_options_icon[data-comment_id="' + e.comment.id + '"]')
                            .length;
                        if (iconExists === 0) {
                            console.log('Comment options icon not found, adding it manually');
                            const commentOptionsIcon = $(
                                '<i class="fa fas fa-ellipsis-h comment_options_icon" data-comment_id="' + e
                                .comment.id +
                                '" style="color: #ff4c00; position: relative; top: -19px; left: 16px; cursor: pointer; z-index: 10;"></i>'
                            );
                            commentRow.find('.comment_text_and_options').append(commentOptionsIcon);
                        }
                    }
                }, 100);

                // Wait for the DOM to update before scrolling
                setTimeout(function() {
                    // Get the position of the newly added comment
                    const newCommentPosition = commentRow.offset().top;

                    // Scroll to the new comment (at the bottom)
                    $('html, body').animate({
                        scrollTop: newCommentPosition -
                            100 // Subtract some pixels to show context above
                    }, 500);

                    // Add a highlight effect to the new comment
                    setTimeout(function() {
                        commentRow.removeClass('highlight-comment');
                    }, 2000);
                }, 300); // Increased timeout to ensure DOM is fully updated
            });

        // Function to show comments
        function show_comments() {
            $.ajax({
                url: "{{ route('show-comments', ['task_id' => request()->route('task_id')]) }}",
                method: "GET",
                data: {
                    "show_comments": 1
                },
                success: function(response) {
                    response = response.Comments;
                    if (response == '') {
                        $('.no_comments_row').removeClass('d-none').addClass('d-block').css({
                            "height": "10px"
                        });
                        $('.comment-section').css({
                            "padding": "10px",
                            "height": "auto"
                        })
                    } else {
                        $('.no_comments_row').removeClass('d-block').addClass('d-none');
                        // Make sure the comment rows container is visible
                        $('.comment_rows').removeClass('d-none').addClass('d-block');
                        // Reset the comment section height and ensure proper spacing
                        $('.comment-section').css({
                            "padding": "10px",
                            "height": "auto",

                        });
                        // Add margin and padding to prevent overlap with footer
                        $('.comment_rows').css({

                        });
                        // Clear any existing comments
                        $('.comment_rows').html("");
                        if (Array.isArray(response)) {
                            response.forEach((comment_details, index) => {
                                // Convert both IDs to numbers to ensure proper comparison
                                const isCurrentUser = parseInt(comment_details.user_id) === parseInt(
                                    {{ Auth::id() }});
                                var comment_rows = $('.comment_rows');
                                var comment_row = $("<div class='comment_row' data-comment_id=" +
                                    comment_details.id + "></div>");
                                var comment_body = $("<div class='comment_body scrollbar'></div>");

                                let formattedComment = comment_details.comment_body;


                                // Standardize HTML content for consistent rendering
                                console.log('Processing loaded comment:', formattedComment);

                                // First sanitize the HTML to ensure consistent formatting
                                formattedComment = sanitizeHtml(formattedComment);

                                // If the comment doesn't already have mention spans, we need to add them
                                if (!formattedComment.includes('class="mentioned-user"')) {
                                    // We need to identify full names that were mentioned
                                    // First, get all the mentioned users from the database or the users list
                                    const mentionedNames = allMentionableUsers.map(user => user.name);

                                    mentionedNames.forEach(name => {
                                        const mentionRegex = new RegExp('@' + name.replace(
                                                /[.*+?^${}()|[\]\\]/g, '\\$&') +
                                            '(?=\\s|$)', 'g');
                                        formattedComment = formattedComment.replace(
                                            mentionRegex, '<span class="mentioned-user">@' +
                                            name + '</span>');
                                    });
                                }

                                // Ensure proper handling of HTML entities
                                formattedComment = formattedComment.replace(/&lt;/g, '&amp;lt;')
                                    .replace(/&gt;/g, '&amp;gt;');

                                console.log('Processed loaded comment:', formattedComment);



                                var comment_text = $("<div class='comment-text'>" + formattedComment +
                                    "</div>");




                                var comment_timestamp = $("<div class='comment-timestamp'>" + timeAgo(
                                    comment_details.created_at) + "</div>");

                                // Only add options icon if it's the current user's message
                                let comment_text_and_options;
                                if (isCurrentUser) {
                                    var comment_options_icon = $(
                                        "<i class='fa fa-ellipsis-h comment_options_icon' data-comment_id=" +
                                        comment_details.id + "></i>");
                                    comment_text_and_options = $(
                                        "<div class='comment_text_and_options d-flex '></div>");
                                    comment_text_and_options.append(comment_text).append(
                                        comment_options_icon);

                                    var dropdown = $(
                                        "<div class='dropdown-container d-none' data-comment_id=" +
                                        comment_details.id +
                                        "><div><a class='edit-comment dropdown-links' data-comment_id=" +
                                        comment_details.id +
                                        ">Edit</a></div><div><a class='delete-comment dropdown-links' data-comment_id=" +
                                        comment_details.id + ">Delete</a></div></div>");
                                    comment_body.append(comment_text_and_options).append(
                                        comment_timestamp).append(dropdown);
                                } else {
                                    comment_text_and_options = $(
                                        "<div class='comment_text_and_options d-flex '></div>");
                                    comment_text_and_options.append(comment_text);
                                    comment_body.append(comment_text_and_options).append(
                                        comment_timestamp);
                                }

                                var attachment_container = $(
                                    "<div class='attachment_container scrollbar'></div>").append(
                                    "<span class='me-2'>Attachments (" + comment_details
                                    .commentAttachments.length + "): </span>");
                                if (comment_details.commentAttachments && comment_details
                                    .commentAttachments.length > 0) {
                                    $.each(comment_details.commentAttachments, function(i,
                                        attachment_path) {
                                        // Get file details
                                        var fileName = attachment_path.comment_attachment;
                                        var fileExtension = fileName.split('.').pop()
                                            .toLowerCase();
                                        var imageUrl = '/storage/uploads/' + fileName;
                                        var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp',
                                            'webp', 'svg'
                                        ].includes(fileExtension);

                                        // Create attachment element
                                        var attachmentElement = $(
                                            "<div class='attachment-item me-2 mb-2'></div>");

                                        if (isImage) {
                                            // For images, show a thumbnail preview
                                            attachmentElement.append(`
                                            <div class="attachment-preview">
                                                <img src="${imageUrl}" alt="${fileName}" class="img-thumbnail" style="height: 50px; width: auto;">
                                            </div>
                                            <div class="attachment-name text-dark">${fileName}</div>
                                        `);
                                        } else {
                                            // For non-images, show an icon based on file type
                                            var fileIcon = 'fa-file';
                                            if (['pdf'].includes(fileExtension)) fileIcon =
                                                'fa-file-pdf';
                                            else if (['doc', 'docx'].includes(fileExtension))
                                                fileIcon = 'fa-file-word';
                                            else if (['xls', 'xlsx'].includes(fileExtension))
                                                fileIcon = 'fa-file-excel';
                                            else if (['zip', 'rar', '7z'].includes(
                                                    fileExtension)) fileIcon =
                                                'fa-file-archive';

                                            // Simplified approach for formatting buttons
                                            // We'll only apply formatting when buttons are clicked with text selected
                                            // No need to constantly update button states

                                            // Function to reset format button states
                                            function resetFormatButtonStates() {
                                                $('.format-btn').removeClass('active');
                                            }

                                            // Format buttons click handlers - simplified approach
                                            $('.formatting-toolbar .format-btn').off('click')
                                                .on('click', function() {
                                                    const format = $(this).data('format');
                                                    const editor = $('#comment-input');
                                                    const selection = window.getSelection();

                                                    // Focus the editor first
                                                    editor.focus();

                                                    if (format === 'mention') {
                                                        // Handle mention button separately
                                                        insertMentionAtCursor();
                                                    } else {
                                                        // Only apply formatting if there's a selection
                                                        if (selection.rangeCount > 0 && !
                                                            selection.isCollapsed) {
                                                            // Apply the formatting command
                                                            document.execCommand(format,
                                                                false, null);

                                                            // Toggle active state on the button that was clicked
                                                            $(this).toggleClass('active');
                                                        }
                                                    }
                                                });

                                            // Add a keyboard shortcut for @ mentions
                                            $('#comment-input').off('keydown.mention').on(
                                                'keydown.mention',
                                                function(e) {
                                                    // Ctrl+@ or Ctrl+Shift+2 for mention (common keyboard shortcut for @)
                                                    if (e.ctrlKey && (e.key === '@' || (e
                                                            .shiftKey && e.key === '2'))) {
                                                        e.preventDefault();
                                                        insertMentionAtCursor();
                                                    }
                                                });
                                            attachmentElement.append(`
                                            <div class="attachment-icon">
                                                <i class="fas ${fileIcon} fa-2x"></i>
                                            </div>
                                            <div class="attachment-name text-white">${fileName}</div>
                                        `);
                                        }

                                        // Make the entire element clickable for preview/download
                                        attachmentElement.addClass('preview_img_span')
                                            .attr('data-img_url', imageUrl)
                                            .attr('title', fileName)
                                            .css({
                                                'cursor': 'pointer',
                                                'display': 'inline-block',
                                                'border': '1px solid #6c757d',
                                                'border-radius': '5px',
                                                'padding': '5px',
                                                'margin-right': '10px',
                                                'text-align': 'center',
                                                'max-width': '150px'
                                            });

                                        attachment_container.append(attachmentElement);
                                    });

                                    attachment_container.css({
                                        'display': 'flex',
                                        'flex-wrap': 'wrap',
                                        'align-items': 'center',
                                        'margin-top': '10px',
                                        'width': '99.3%'
                                    });

                                    comment_body.append(attachment_container);
                                }

                                comment_row.append("<img src='" + comment_details.user_profile_image +
                                    "' height='40px' width='40px' class='comment-img rounded rounded-5 mb-1'>"
                                )
                                comment_row.append(comment_body);

                                // Append to comment_rows (already in correct order since server returns oldest first)
                                comment_rows.append(comment_row);
                            });
                        }
                    }
                }
            });
        }


        show_comments();


        $('input[name="myfile"]').on('change', function(event) {
            var fileInput = this;
            var commentAttachmentsDiv = $('.meta-text').find('.comment_attachments');

            if (commentAttachmentsDiv.length === 0) {
                commentAttachmentsDiv = $("<div class='comment_attachments'></div>").addClass("scrollbar").css({
                    "padding": "0px 10px",
                    "height": "50px",
                    "overflow-y": "hidden",
                    "display": "flex",
                    "align-items": "center"
                });
                $('.meta-text').append(commentAttachmentsDiv);
            }

            if (fileInput.files && fileInput.files.length > 0) {
                for (var i = 0; i < fileInput.files.length; i++) {
                    var file = fileInput.files[i];
                    var existingIndex = selectedFiles.findIndex(sf => sf.name === file.name && sf.size === file
                        .size && sf.type === file.type);

                    if (existingIndex === -1) {
                        selectedFiles.push(file);
                        displayPreview(file, selectedFiles.length - 1);
                    }
                }
                updateFileInput();
            }
        });

        function displayPreview(file, index) {
            var objectUrl = URL.createObjectURL(file);
            var previewContainer = $(
                "<div class='preview-container' style='margin-right: 15px;position: relative;display:flex'></div>");
            var previewImg = $(
                "<span class='border border-secondary me-2 p-1 rounded text-white preview_img_span' data-img_url='" +
                objectUrl +
                "' style='cursor:pointer; max-width:150px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis' title='" +
                file.name + "'>" + file.name + "</span>");
            var closeButton = $("<button type='button' class='close-preview' data-index='" + index +
                "' style='position: absolute; top: -5px; right: -5px; border: none; color: red; font-size: 14px; border-radius: 50%; padding:0px 5px; cursor: pointer;'>&times;</button>"
            );

            previewContainer.append(previewImg);
            previewContainer.append(closeButton);
            $('.comment_attachments').append(previewContainer);

            previewImg.on('load', function() {
                URL.revokeObjectURL(objectUrl);
            });
            previewContainer.on('removed', function() {
                URL.revokeObjectURL(objectUrl);
            });

            closeButton.on('click', function() {
                var indexToRemove = parseInt($(this).data('index'));
                selectedFiles.splice(indexToRemove, 1);
                $(this).parent('.preview-container').remove();
                updateFileInput();

                $('.comment_attachments .preview-container').each(function(newIndex) {
                    $(this).find('.close-preview').data('index', newIndex);
                });
            });
        }

        function updateFileInput() {
            var fileInput = $('input[name="myfile"]')[0];
            var dataTransfer = new DataTransfer();

            formData.delete("comment_attachments[]");

            // Handle selectedFiles
            selectedFiles.forEach(function(file) {
                if (file instanceof File) {
                    formData.append("comment_attachments[]", file);
                    dataTransfer.items.add(file);
                } else {
                    console.warn('Skipped non-File object in selectedFiles:', file);
                }
            });

            // Handle dragAndDroppedFiles
            dragAndDroppedFiles.forEach(function(fileObj) {
                if (fileObj.file instanceof File) {
                    formData.append("comment_attachments[]", fileObj.file);
                    dataTransfer.items.add(fileObj.file);
                } else {
                    console.warn('Skipped invalid file in dragAndDroppedFiles:', fileObj);
                }
            });

            fileInput.files = dataTransfer.files;

            if ($('.comment_attachments').children().length == 0) {
                $('.comment_attachments').css({
                    "height": "0px"
                })
            } else {
                $('.comment_attachments').css({
                    "height": "50px"
                })
            }
        }


        $('.submit-comment-btn').on('click', function(event) {
            event.preventDefault();

            // Get the HTML content from the rich editor
            let commentHtml = $('#comment-input').html();

            // Also get plain text for validation
            let commentText = $('#comment-input').text();

            if (commentText.trim() !== '') {
                // Run the link detection one more time to ensure all links are properly formatted
                autoDetectLinks();

                // Make sure any text formatting is preserved
                // This ensures bold and italic formatting is maintained
                const editor = $('#comment-input');

                // Get the updated HTML content after link detection
                commentHtml = editor.html();

                // Store the HTML in the hidden textarea for submission
                $('#comment-input-hidden').val(commentHtml);

                // Use our sanitizeHtml function to ensure consistent formatting
                commentHtml = sanitizeHtml(commentHtml);

                console.log('Submitting comment HTML:', commentHtml);

                let taskId = document.getElementById('mark-complete').getAttribute('data-id');

                // Create a new FormData object for this submission to avoid any stale data
                const submitFormData = new FormData();

                // Add the comment HTML content
                submitFormData.append('comment-body', commentHtml);

                submitFormData.append('task_id', taskId);

                // Extract mentioned users from the HTML content
                const mentionedElements = $('#comment-input').find('.mentioned-user');
                const mentionedUserIds = [];


                mentionedElements.each(function() {
                    const userId = $(this).attr('data-user-id');

                    if (userId && !mentionedUserIds.includes(userId)) {
                        mentionedUserIds.push(userId);
                    }
                });

                console.log("Mentioned user IDs:", mentionedUserIds);

                // Add mentioned users to the form data
                if (mentionedUserIds.length > 0) {
                    mentionedUserIds.forEach(userId => {
                        submitFormData.append('mentioned_users[]', userId);
                    });
                }


                if (dragAndDroppedFiles.length > 0) {
                    dragAndDroppedFiles.forEach(function(fileObj) {
                        // Access the actual File object from our structure
                        if (fileObj.file instanceof File) {
                            submitFormData.append('comment_attachments[]', fileObj.file);
                        }
                    });
                }

                // Add any file attachments
                if (selectedFiles.length > 0) {

                    console.log('inside');


                    selectedFiles.forEach(file => {
                        submitFormData.append('comment_attachments[]', file);
                    });
                }


                submitFormData.append('_token', $("input[name='_token']").val());

                $.ajax({
                    url: "{{ route('save-comment', ['task_id' => request()->route('task_id')]) }}",
                    method: "POST",
                    data: submitFormData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': $("input[name='_token']").val()
                    },
                    success: function(response) {
                        // Clear the arrays properly
                        dragAndDroppedFiles = [];
                        selectedFiles = [];

                        // Clear the comment input
                        $('#comment-input').html("").focus();

                        // Clear the form data and attachments
                        formData = new FormData();
                        $('.comment_attachments').html("");
                        $("input[name='myfile']").val("");
                        selectedFiles = [];
                        updateFileInput();

                        // No need to call show_comments() as the comment will be added via the Echo event
                        // This prevents duplicate comments and scrolling issues
                        console.log("Comment submitted successfully");


                        $('#mention-data').val('[]');
                        selectedUsers.clear();


                        mentionStart = -1;
                        currentMention = '';
                        selectedIndex = -1;
                    },
                    error: function(xhr, status, error) {
                        console.error('Error posting comment:', error);
                        alert('There was an error posting your comment. Please try again.');
                    }
                });
            } else {
                alert('Please enter a comment');
            }
        });


        // Handle status changes
        $('.flag-status').on('click', function() {
            const statusId = $(this).data('status_id');
            const taskId = {{ $task->id }};

            $.ajax({
                url: "{{ route('save-comment', ['task_id' => request()->route('task_id')]) }}",
                method: "POST",
                data: {
                    "edit-task-status": 1,
                    "task_status": statusId,
                    "_token": "{{ csrf_token() }}"
                },
                success: function(response) {
                    $('.flag-status').removeClass('assigned');
                    $(`.flag-status[data-status_id="${statusId}"]`).addClass('assigned');
                }
            });
        });

        // Handle user assignment changes
        $('.assigned_to_checkbox').on('change', function() {
            const selectedUsers = [];

            // Get all checked users
            $('.assigned_to_checkbox:checked').each(function() {
                selectedUsers.push($(this).attr('id'));
            });

            // Check if at least one checkbox is checked for assignment
            if (selectedUsers.length < 1) {
                $('.assign_to_users_error').text('Please select at least one user to assign.');
                return; // Stop further execution (including AJAX)
            } else {
                $('.assign_to_users_error').text(''); // Clear any previous error message
            }

            $.ajax({
                url: "{{ route('save-comment', ['task_id' => request()->route('task_id')]) }}",
                method: "POST",
                data: {
                    "edit-assign-to": 1,
                    "assign_to": selectedUsers,
                    "_token": "{{ csrf_token() }}"
                },
                success: function(response) {
                    if (response.error) {
                        $('.assign_to_users_error').text(response.error);
                    } else {
                        $('.assign_to_users_error').text('');
                    }
                }
            });
        });

        $('.due-date').on('change', function() {
            updateLabel(event)

            $.ajax({
                url: "{{ route('save-comment', ['task_id' => request()->route('task_id')]) }}",
                method: "POST",
                data: {
                    "edit-due-date": 1,
                    "due-date": $(this).val(),
                    "_token": "{{ csrf_token() }}"
                },
                success: function(response) {

                }
            });
        })

        // Function to scroll to a specific comment
        function scrollToComment(commentId) {
            const commentElement = $(`[data-comment_id="${commentId}"]`);
            if (commentElement.length) {
                // Apply temporary styling to ensure the comment is visible
                commentElement.css({
                    'margin-bottom': '15px',
                    'background-color': 'rgba(255, 76, 0, 0.1)' // Initial highlight
                });

                // Create space for the comment
                $('.comment_rows').css('margin-bottom', '150px');

                // Scroll to the comment with offset
                $('html, body').animate({
                    scrollTop: commentElement.offset().top - 100
                }, 500);

                // Highlight the comment
                commentElement.addClass('highlight-comment');
                setTimeout(() => {
                    commentElement.removeClass('highlight-comment');
                    commentElement.css('background-color', ''); // Remove initial highlight
                }, 2000);
            }
        }


        $(document).ready(function() {
            // Initialize the comment section structure
            if ($('.comment_rows').length === 0) {
                // If the comment_rows container doesn't exist, create it
                const commentRowsContainer = $('<div class="comment_rows text-white d-block"></div>');

                $('.comment-section').append(commentRowsContainer);
            } else {

            }



            updateMentionedUsers();

            const urlParams = new URLSearchParams(window.location.search);
            const commentId = urlParams.get('comment_id');

            if (commentId) {
                // Wait for comments to load
                setTimeout(() => {
                    scrollToComment(commentId);
                }, 1000);
            }
        });

        $(document).on('click', '.delete-comment', function(event) {
            event.preventDefault();
            const commentId = $(this).data('comment_id');

            if (confirm('Are you sure you want to delete this comment?')) {
                $.ajax({
                    url: "{{ route('delete-comment') }}",
                    method: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        comment_id: commentId
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            // Remove the comment from the UI
                            $(`[data-comment_id="${commentId}"]`).remove();

                            // If no comments left, show the "no comments" message
                            if ($('.comment_row').length === 0) {
                                $('.no_comments_row').removeClass('d-none').addClass('d-block');
                            }
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function(xhr) {
                        alert('Error deleting comment. Please try again.');
                    }
                });
            }
        });

        $(document).on('click', '.comment_options_icon', function(event) {
            event.preventDefault();
            const commentId = $(this).data('comment_id');
            const dropdown = $(`.dropdown-container[data-comment_id="${commentId}"]`);

            // Hide all other dropdowns
            $('.dropdown-container').not(dropdown).addClass('d-none');

            // Toggle the clicked dropdown
            dropdown.toggleClass('d-none');
        });

        // Close dropdown when clicking outside
        $(document).on('click', function(event) {
            if (!$(event.target).closest('.comment_options_icon, .dropdown-container').length) {
                $('.dropdown-container').addClass('d-none');
            }
        });

        $(document).on('click', '.edit-comment', function(event) {
            event.preventDefault();
            const commentId = $(this).data('comment_id');
            const commentRow = $(`.comment_row[data-comment_id="${commentId}"]`);

            // Get the HTML content of the comment text div
            // We need to preserve the HTML to maintain formatting
            const commentHtml = commentRow.find('.comment-text').html();

            // Create a rich text editor for editing instead of a plain textarea
            // This will allow us to preserve formatting like bold and italic
            const editContainer = $(`<div class="rich-editor-container edit-comment-container"></div>`);

            // Add formatting toolbar
            const toolbar = $(`
            <div class="formatting-toolbar">
             <div class="format-tip">
    💡 Tip: Use <strong>Ctrl+B</strong> for bold, <strong>Ctrl+I</strong> for italic, and <strong>Ctrl+U</strong> for underline. Press the same keys again to remove the format.
</div>

            </div>
        `);

            // Create the editable div with the original HTML content
            const editableDiv = $(
                `<div class="edit-comment-input rich-editor" contenteditable="true" data-original-html="${encodeURIComponent(commentHtml)}">${commentHtml}</div>`
            );

            // Add the toolbar and editable div to the container
            editContainer.append(toolbar).append(editableDiv);

            // Create save and cancel buttons
            const saveButton = $(`<button class="save-edit-btn" data-comment_id="${commentId}">Save</button>`);
            const cancelButton = $(
                `<button class="cancel-edit-btn" data-comment_id="${commentId}">Cancel</button>`);

            // Replace the comment text with the edit container
            commentRow.find('.comment-text').replaceWith(editContainer);
            commentRow.find('.comment_options_icon').hide();

            // Add the buttons
            const buttonContainer = $('<div class="edit-buttons"></div>');
            buttonContainer.append(saveButton, cancelButton);
            commentRow.find('.comment_body').append(buttonContainer);

            // Simplified format buttons setup for edit comment
            editContainer.find('.format-btn').on('click', function() {
                const format = $(this).data('format');
                const selection = window.getSelection();

                // Focus the editor first
                editableDiv.focus();

                // Only apply formatting if there's a selection
                if (selection.rangeCount > 0 && !selection.isCollapsed) {
                    // Apply the formatting command
                    document.execCommand(format, false, null);

                    // Toggle active state on the button that was clicked
                    $(this).toggleClass('active');
                }
            });

            // Add keyboard shortcuts for the edit comment functionality
            editableDiv.on('keydown', function(e) {
                const selection = window.getSelection();

                // Only apply formatting if there's a selection
                if (selection.rangeCount > 0 && !selection.isCollapsed) {
                    // Ctrl+B for bold
                    if (e.ctrlKey && e.key === 'b') {
                        e.preventDefault();
                        document.execCommand('bold', false, null);
                        editContainer.find('.format-btn[data-format="bold"]').toggleClass('active');
                    }
                    // Ctrl+I for italic
                    else if (e.ctrlKey && e.key === 'i') {
                        e.preventDefault();
                        document.execCommand('italic', false, null);
                        editContainer.find('.format-btn[data-format="italic"]').toggleClass('active');
                    }
                    // Ctrl+U for underline
                    else if (e.ctrlKey && e.key === 'u') {
                        e.preventDefault();
                        document.execCommand('underline', false, null);
                        editContainer.find('.format-btn[data-format="underline"]').toggleClass('active');
                    }
                }
            });

            // Function to reset format button states
            function resetEditFormatButtonStates() {
                editContainer.find('.format-btn').removeClass('active');
            }

            // Reset button states when clicking in the editor
            editableDiv.on('click', function() {
                resetEditFormatButtonStates();
            });


            // Focus the editable div
            editableDiv.focus();
        });

        $(document).on('click', '.save-edit-btn', function() {
            const commentId = $(this).data('comment_id');
            const commentRow = $(`.comment_row[data-comment_id="${commentId}"]`);

            // Get the rich text editor content
            const editInput = commentRow.find('.edit-comment-input');
            const newHtml = editInput.html();

            // Process the HTML to ensure proper formatting
            // We'll use our sanitizeHtml function to maintain consistency
            const processedText = sanitizeHtml(newHtml);

            $.ajax({
                url: "{{ route('edit-comment', ['comment_id' => 'placeholder']) }}".replace('placeholder',
                    commentId),
                method: "POST",
                data: {
                    _token: "{{ csrf_token() }}",
                    'edit-comment-input': newHtml
                },
                success: function(response) {
                    if (response.success) {
                        // Create a new comment-text div with the processed text
                        const newCommentText = $(`<div class="comment-text"></div>`).html(
                            processedText);

                        // Replace the edit container with the new comment text
                        commentRow.find('.edit-comment-container').replaceWith(newCommentText);

                        // Clean up the edit UI
                        commentRow.find('.edit-buttons').remove();
                        commentRow.find('.comment_options_icon').show();
                    } else {
                        alert(response.message || 'Failed to update comment.');
                    }
                },
                error: function(xhr) {
                    let message = 'An unexpected error occurred.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    alert(message);
                }
            });
        });

        // Handle cancel edit
        $(document).on('click', '.cancel-edit-btn', function() {
            const commentId = $(this).data('comment_id');
            const commentRow = $(`.comment_row[data-comment_id="${commentId}"]`);
            const editInput = commentRow.find('.edit-comment-input');

            // Get the original HTML from the data attribute
            const originalHtml = decodeURIComponent(editInput.data('original-html'));

            // Restore original comment HTML to preserve formatting
            commentRow.find('.edit-comment-container').replaceWith(
                `<div class="comment-text">${originalHtml}</div>`);
            commentRow.find('.edit-buttons').remove();
            commentRow.find('.comment_options_icon').show();
        });

        // Handle attachment preview and download
        $(document).on('click', '.preview_img_span', function(e) {


            e.preventDefault();
            const imgUrl = $(this).data('img_url');
            

            const fileName = $(this).text().trim();
            const fileExtension = fileName.split('.').pop().toLowerCase();

            // List of image extensions
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];

            if (imageExtensions.includes(fileExtension)) {
                // It's an image, show preview in modal
                $('.modal-image').attr('src', imgUrl);

                $('.modal-heading').text(fileName);
                $('#imageModal').modal('show');
            } else {

                window.location.href = imgUrl;
            }
        });
    </script>
@endpush
