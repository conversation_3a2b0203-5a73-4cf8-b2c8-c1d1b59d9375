<?php

namespace App\Http\Controllers;

use App\Events\CommentAdded;
use App\Events\CommentCreated;
use App\Models\AccessLevel;
use App\Models\Comments;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Brand;
use App\Models\Permission;
use App\Models\Project;
use App\Models\Role;
use App\Models\Task;
use App\Models\Phase;
use App\Models\Status;
use App\Models\Category;
use App\Notifications\CommentAddedNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;

use App\Notifications\TaskAssigned;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Notifications\MentionedInComment;


class DashboardController extends Controller
{






    public function index(Request $request)
    {

        $admin_access_level = AccessLevel::where('name', '=', 'Admin')->first();
        if (Auth::user()->role->name == 'SuperAdmin') {
            return redirect()->route('admin-dashboard');
        } elseif (Auth::user()->role->name == 'Admin') {
            return redirect()->route('admin-dashboard');

            // $projects = Project::with(['users'])->where('assigned_by', Auth::user()->id)->get();
            // $company = Brand::with(['projects'])->where('user_id', '=', Auth::user()->id)->first();
            // if ($company) {
            //     $company = $company->projects;
            // } else {
            //     $company = [];
            // }
        } else if ($admin_access_level && Auth::user()->access_level_id === $admin_access_level->id) {
            return redirect()->route('admin-dashboard');
        } else if(Auth::user()->role->name == 'Client Member') {
            return redirect()->route('client.dashboard');
        } else {
            $user = User::findOrFail(Auth::user()->id);

            $projects = $user->projects()
                ->with(['tasks' => function ($query) use ($user) {
                    $query->whereHas('users', function ($q) use ($user) {
                        $q->where('users.id', $user->id);
                    })->with('status');
                }])
                ->get();

            // Initialize grouped tasks instead of grouped projects
            $groupedTasks = [
                'urgent' => [],
                'regular' => [],
            ];

            // Group tasks by their status
            foreach ($projects as $project) {
                foreach ($project->tasks as $task) {
                    $status = strtolower($task->status->name ?? '');

                    if ($status === 'urgent') {
                        $groupedTasks['urgent'][] = $task;
                    } else if ($status === 'new') {
                        $groupedTasks['regular'][] = $task;
                    }
                }
            }
        }
        return view('dashboard', compact('groupedTasks', 'projects'));
    }






    public function show_profile()
    {
        $user = User::find(Auth::user()->id)
            ->leftJoin('roles', 'users.role_id', '=', 'roles.id')
            ->select('users.id', 'users.name', 'users.email', 'roles.name as role')
            ->first();
        return view('profile', ['user' => $user]);
    }




    // ===============================  Roles ====================================

    public function show_roles()
    {
        $roles = Role::all();
        return view("roles", ['roles' => $roles]);
    }

    public function add_role()
    {
        return view('add-role');
    }

    public function save_role(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'role_name' => ['required', 'unique:roles,name,' . $request->route("role_id")],
        ]);

        if (Validator::fails($validate)) {
            return back()->withErrors($validate)->withInput();
        }

        $role = new Role;
        $role->name = $request->input("role_name");
        $role->save();
        if ($role) {
            return back()->with("role_saved_success_message", "Role Saved Successfully");
        } else {
        }
    }

    public function edit_role(Request $request)
    {
        $role = Role::with(['permissions'])->where("id", '=', $request->route("role_id"))->first();
        $permissions = Permission::all();
        if ($role) {
            return view('edit-role', ['role' => $role, 'permissions' => $permissions]);
        } else {
            return view('edit-role', ['no_roles_found_error' => 'Role Not Found']);
        }
    }

    public function update_role(Request $request)
    {

        $validate = Validator::make($request->all(), [
            'role_name' => 'required|unique:roles,name,' . $request->route("role_id"),
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }

        $role = Role::where('id', '=', $request->route("role_id"))->first();
        $role->name = $request->input("role_name");
        $role->save();


        // Get the array of permission IDs to sync (from the form request)
        $permissions = $request->input('role_permissions', []); // Default to empty array if no permissions selected

        // Sync the permissions (this will attach and detach as necessary)
        $role->permissions()->syncWithoutDetaching($permissions); //IMP

        if ($role) {
            return back()->with("role_updated_success_message", "Role Updated Successfuly");
        }
    }

    public function delete_role(Request $request)
    {
        $role = Role::where('id', '=', $request->route("role_id"))->first();
        $role_name = $role->name;
        if ($role) {
            $role->delete();
            if ($role) {
                return back()->with("role_deleted_success_message", "Role: '" . $role_name . "' deleted successfully");
            } else {
            }
        }
    }


    // =============================== Permissions ======================================


    public function show_permissions()
    {

        $permissions = Permission::all();
        return view('admin.permissions.index', ['permissions' => $permissions]);
    }

    public function add_permission(Request $request)
    {
        return view('admin.permissions.add_permissions');
    }

    public function save_permission(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'permission_name' => ['required', 'unique:permissions,name'],
        ]);
        if ($validate->fails()) {
            return redirect()->back()->withErrors($validate)->withInput();
        }

        $permission = new Permission;
        $permission->name = $request->input("permission_name");
        $permission->save();
        if ($permission) {
            return back()->with("permission_saved_success_message", "Permission Saved Successfully");
        }
    }

    public function edit_permission(Request $request)
    {
        $permission = Permission::where('id', '=', $request->route("id"))->get();
        return view('admin.permissions.edit_permissions', ['permission' => $permission]);
    }

    public function update_permission(Request $request)
    {
        $permission = Permission::where('id', '=', $request->route("id"))->first();
        $permission->name = $request->input("permission_name");
        $permission->save();
        if ($permission) {
            return back()->with("permission_updated_success_message", "Permission Updated Successfuly");
        }
    }

    public function delete_permission(Request $request)
    {
        $permission = Permission::where('id', '=', $request->route("id"))->first();
        $permission_name = $permission->name;
        if ($permission) {
            $permission->delete();
            if ($permission) {
                return back()->with("permission_deleted_success_message", "Permission: '" . $permission_name . "' deleted successfully");
            } else {
            }
        }
    }

    // =============================== Phases ======================================


    public function add_phase()
    {
        return view('add-phase');
    }


    public function show_phase()
    {
        $phases = Phase::all();
        if ($phases->isNotEmpty()) {
            return view('phases', compact('phases'));
        } else {
            return view('phases', ['no_phase_found_error' => 'No Phase found!!']);
        }
    }

    public function store_phase(Request $request)
    {
        $validate = Validator::make(
            $request->all(),
            [
                'phase_order' => 'required',
                'phase_name' => 'required',
                'phase_duration' => 'required',
                'phase_description' => '',
            ],
            [
                'phase_order.required' => 'This field is required',
                'phase_name.required' => 'This field is  required',
                'phase_duration.required' => 'This field is required'
            ]
        );

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }

        $validate = $validate->validate();
        $phase = new Phase;
        $phase->order = $validate['phase_order'];
        $phase->name = $validate['phase_name'];
        $phase->duration = $validate['phase_duration'];
        $phase->description = $validate['phase_description'];
        $phase->save();
        if ($phase) {
            return back()->with('phase_added_success_message', 'Phase Added Successfully');
        } else {
            return back()->with('phase_added_error_message', 'Phase Not Added');
        }
    }


    public function edit_phase(Request $request)
    {
        $phase = Phase::where('id', '=', $request->route("phase_id"))->first();
        if ($phase) {
            return view('edit-phase', ['phase' => $phase]);
        } else {
            abort(404);
            // return view('edit-phase',['no_phase_found_error' => 'Phase Not Found']);
        }
    }

    public function update_phase(Request $request)
    {
        $validate = Validator::make(
            $request->all(),
            [
                'phase_order' => 'required',
                'phase_name' => 'required',
                'phase_duration' => 'required',
                'phase_description' => '',
            ],
            [
                'phase_order.required' => 'This field is required',
                'phase_name.required' => 'This field is  required',
                'phase_duration.required' => 'This field is required'
            ]
        );

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        };

        $phase = Phase::where('id', '=', $request->route("phase_id"))->first();
        $phase->order = $request->input("phase_order");
        $phase->name = $request->input("phase_name");
        $phase->duration = $request->input("phase_duration");
        $phase->description = $request->input("phase_description");
        $phase->save();
        if ($phase) {
            return back()->with('phase_updated_success_message', 'Phase updated successfully.');
        }
    }

    public function delete_phase(Request $request)
    {
        $phase = Phase::where('id', '=', $request->route("phase_id"));
        if ($phase) {
            $phase->delete();
            if ($phase) {
                return back()->with("phase_deleted_success_message", "Phase deleted successfully");
            } else {
            }
        }
    }

    // ================================ Categories ======================================


    /**
     * Show the form for creating a new category.
     */
    public function add_category()
    {
        $user = User::find(Auth::user()->id);
        if ($user->role && $user->role->name === 'SuperAdmin') {
            $projects = Project::all();
        } else {
            $projects = Project::where('assigned_by', $user->id)->get();
        }
        return view('add-category', compact('projects'));
    }

    /**
     * Display the specified category.
     */
    public function show_categories()
    {
        $categories = Category::all();
        return view('categories', compact('categories'));
    }

    /**
     * Store a newly created category in the database.
     */
    public function store_category(Request $request)
    {

        $validate = Validator::make(
            $request->all(),
            [
                'category_name' => 'required|string|max:255',
                'category_order' => 'required|integer',
                'category_description' => 'nullable|string',
            ],
            [
                'phase_order.required' => 'This field is required',
                'phase_name.required' => 'This field is  required',
            ]
        );

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }

        $validate = $validate->validate();
        $category = new Category;
        $category->name = $validate['category_name'];
        $category->order = $validate['category_order'];
        $category->description = $validate['category_description'];
        $category->save();
        if ($category) {
            return back()->with('category_added_success_message', 'Category Added Successfully');
        } else {
            return back()->with('category_added_error_message', 'Category Not Added');
        }
    }



    /**
     * Show the form for editing the specified category.
     */
    public function edit_category(Request $request)
    {
        $category = Category::where('id', '=', $request->route("category_id"))->first();
        return view('edit-category', compact('category'));
    }

    /**
     * Update the specified category in the database.
     */
    public function update_category(Request $request)
    {
        $request->validate([
            'category_name' => 'required|string|max:255',
            'category_order' => 'required|integer',
            'category_description' => 'nullable|string',
        ], [
            'category_name.required' => 'This field is required',
            'category_order.required' => 'This field is required',
        ]);

        $category = Category::where('id', '=', $request->route('category_id'))->first();

        $category->update([
            'name' => $request->input("category_name"),
            'order' => $request->input("category_order"),
            'description' => $request->input("category_description"),
        ]);

        return back()->with('category_updated_success_message', 'Phase updated successfully.');
    }

    /**
     * Remove the specified category from the database.
     */
    public function delete_category(Request $request)
    {
        $category = Category::where('id', '=', request()->route("category_id"))->first();
        if (!$category) {
            return back()->with('category_not_found_error', 'Category not found.');
        }
        $category->delete();
        return back()->with('category_deleted_success_message', 'Category deleted successfully.');
    }


    // ================================ Projects =======================================


    public function show_projects()
    {
        //If user role is Admin, show all accounts i.e. Admin can access/view all user projects
        if (Auth::user()->role_id == '1') {
            $projects = Project::orderBy('updated_at', 'desc')->get();
            if ($projects->isNotEmpty()) {
                return view('projects', ['projects' => $projects]);
            } else {
                return view('projects', ['no_projects_found_error' => 'No projects found!!']);
            }
        } else {
            $projects = Brand::with(['projects'])->where('user_id', '=', Auth::user()->id)->first()->projects;
            // return $projects;
            if ($projects) {
                return view('projects', ['projects' => $projects]);
            } else {
                return view('projects', ['no_projects_found_error' => 'You are not assigned any projects. Please connect to Admin.']);
            }
        }
    }

    public function show_projects_ajax(Request $request)
    {
        $projects = Project::all();
        return $projects;
    }

    public function add_project()
    {
        return view('project.add_project');
    }

    public function save_project(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'project_name' => 'required|unique:projects,name,' . $request->route('project_id'),
            'project_status' => ''
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        };

        $project = new Project;
        $project->name = $request->input('project_name');
        $project->status_id = "1";

        // 2. Save the new project to the database
        $project->save(); // This will generate and assign the $project->id

        // 3. Process the company IDs for attachment //IMP
        $companyIds = $request->input('company_id');
        if (!is_array($companyIds)) {
            $companyIds = (array) $companyIds;
        }
        $companiesToAttach = [];
        foreach ($companyIds as $companyId) { //IMP
            $companyIdInt = (int) $companyId;
            $companiesToAttach[$companyIdInt] = ['assigned_by_user' => Auth::user()->id];
        }

        // 4. Attach the companies to the newly created project
        $project->companies()->syncWithoutDetaching($companiesToAttach);

        if ($project) {
            return back()->with('project_added_success_message', 'Project Added successfully.');
        } else {
            return back()->with('project_not_added_error_message', 'Project not Added successfully.');
        }
    }

    public function edit_project(Request $request)
    {
        $project = Project::where('id', '=', $request->route("project_id"))->first();
        if ($project) {
            $selected_companies = $project->companies()->get();
            // $companies = Company::all();
            $company_id = [];
            foreach ($selected_companies as $selected_company) { //IMP
                $selected_company_array[] = $selected_company->pivot->company_id;
            }
            $status = Status::all();
            if ($project->companies()->first()->pivot) {
                // $company_id = $project->companies()->first()->pivot->company_id;//company_id is not stored in projects table so it will be accessed from pivot table of companies and projects table

                // $company = Company::where('id', '=', $company_id)->first();

                return view('edit-project', ['project' => $project, 'company_ids' => $selected_company_array, 'status' => $status]);
            }
        } else {
            // abort(404);
            return view('edit-project', ['no_projects_found_error' => 'Project Not Found']);
        }
    }

    public function update_project(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'project_name' => 'required|unique:projects,name,' . $request->route('project_id'),
            'project_status' => ''
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        };

        $project = Project::where('id', '=', $request->route("project_id"))->first();
        $project->status_id = $request->input('status_id');

        // Or, more efficiently if you have an array of company IDs:
        $companyIds = $request->input('company_id');
        if (!is_array($companyIds)) {
            $companyIds = (array) $companyIds; // Cast to an array
        }
        foreach ($companyIds as $companyId) {
            $companyIdInt = (int) $companyId; //convert to integer such that array should have ony integer values whether array element = 1 or more
            $companiesToAttach[$companyIdInt] = ['assigned_by_user' => Auth::user()->id];
        }
        $project->companies()->syncWithoutDetaching($companiesToAttach);



        // Check if any changes were made to the fields
        $isUpdated = false;

        // Check if project_name has changed
        if ($request->has('project_name') && $request->input('project_name') !== '' && $project->name !== $request->input('project_name')) {
            $project->name = $request->input('project_name');
            $isUpdated = true;
        }

        // Check if project_status has changed
        if ($request->has('project_status') && $request->input('project_status') !== '' && $project->status !== $request->input('project_status')) {
            $project->status = $request->input('project_status');
            $isUpdated = true;
        }

        // If any change has occurred, save the updated project
        // if ($isUpdated) {
        $project->save();
        if ($project) {
            return back()->with('project_updated_success_message', 'Project updated successfully.');
        } else {
            return back()->with('project_not_updated_error_message', 'Project not updated successfully.');
        }
        // }
        // return back()->with('project_updated_success_message', 'No changes made to the project.');

    }

    public function delete_project(Request $request)
    {
        $project = Project::where('id', '=', $request->route("project_id"));
        if ($project) {
            $project->delete();
            if ($project) {
                return back()->with("project_deleted_success_message", "Project deleted successfully");
            } else {
            }
        }
    }


    public function show_single_project_tasks(Request $request)
    {
        $project_id = $request->route('project_id');
        $project = Project::findOrFail($project_id);
        return view('project.tasks', compact('project'));
    }

    // ========================= Tasks ======================================


    public function add_task(Request $request)
    {
        $project_id = $request->route('project_id');
        $project = Project::with('users')->findOrFail($project_id); // fixed
        $statuses = Status::all();
        return view('add-task', compact('statuses', 'project'));
    }

    public function save_task(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'task_status' => 'required|string|max:255',
            'task_name' => 'required|string|max:255|unique:tasks,name',
            'task_description' => 'required|string',
            // 'task_assigned_to' => 'required|array',  // Ensure it's an array
            // 'task_assigned_to.*' => 'exists:users,id', // Ensure each user ID exists
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }

        $task = new Task();
        $task->project_id = $request->input('project_id');
        $task->name = $request->input('task_name');
        $task->description = $request->input('task_description');
        $task->status_id = $request->input('task_status');
        $task->save();

        // Attach multiple users to the task
        if ($request->input('task_assigned_to')) {
            $task->users()->attach($request->input('task_assigned_to'));
        }

        $users = $request->input('task_assigned_to', []);
        $usersToAttach = is_array($users) ? $users : [(int) $users];

        foreach ($usersToAttach as $user) {
            try {
                $user = User::find($user);
                $user->notify(new TaskAssigned($task));
            } catch (\Exception $e) {
                Log::error("Notification failed for user ID $user->name: " . $e->getMessage());
                // Optional: show debug error temporarily

            }
        }

        return back()->with('task_saved_success_message', 'Task saved successfully!');
    }

    public function edit_task(Request $request)
    {
        $task = Task::where('id', '=', $request->route('task_id'))->first();
        if ($task) {
            $project_id = $task->project_id;
            $status = Status::all();
            return view('edit-task', ['task' => $task, 'status' => $status]);
        } else {
            return view('edit-task', ['task_not_found_error' => 'Task Not Found']);
        }
    }

    public function load_task_users_in_dropdown($task_id)
    {
        $task = Task::where('id', '=', $task_id)->first();
        // return $task;
        if ($task) {
            $users = $task->users()->get()->map(function ($user) { //IMP
                return $user->makeHidden('pivot');
            });
            return $users->select("id", "name");
        }
    }

    public function update_task(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'task_name' => '',
            'task_status' => '',
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }

        $task = Task::find($request->route('task_id'));
        $task->name = $request->input('task_name');
        $task->status = $request->input('task_status');
        $task->save();
        if ($task) {
            return back()->with('task_updated_success_message', 'Record Updated Succesfully');
        } else {
            return back()->with('task_not_updated_success_message', "Project was not updated successfully!");
        }
    }

    public function delete_task(Request $request)
    {
        $task = Task::where('id', '=', $request->route("task_id"));
        if ($task) {
            $task->delete();
            if ($task) {
                return back()->with("task_deleted_success_message", "Task deleted successfully");
            } else {
            }
        }
    }



    // ========================= User Accounts ======================================

    //shows all user accounts to admin on accounts page
    public function show_user_accounts_on_accounts_page(Request $request)
    {
        //If user role is Admin, show all accounts i.e. Admin can access/view all user accounts
        // if(Auth::user()->role_id=='1'){

        $users = User::all();

        return view('accounts', ['users' => $users]);
        // }

        // return  ['not_authorized_error_msg'=>'You are not authorized to access this page'];
        abort(403, "YOU ARE NOT ALLOWED TO ACCESS THIS PAGE");
    }

    public function user_accounts_list_ajax(Request $request)
    {
        $users = User::all();
        return $users;
    }

    //shows edit user accounts functionality to admin on accounts page
    public function edit_user(Request $request)
    {
        $user = User::with(['companies.projects'])->where('id', '=', $request->route('id'))->get();
        $roles = Role::with(['permissions'])->get();
        // return $roles;

        // return $user;

        if ($user->isNotEmpty()) {
            if ($request->query("fetch_user_data")) {
                // return $user;//on edit page, ajax request triggers this condition and fetches the data i.e first the view is displayed then data is fetched using ajax
            }
            return view('edit-user', ['user' => $user, 'roles' => $roles]);
        } else {
            // return ['user_not_found_error' => "User Not Found!"];
            // return;
            abort(404, "User Not Found");
        }

        return view('edit-user'); //if this route is hit, we simply go to edit-user page without data. There, we run an ajax request to fetch all data and also send extra parameter "fetch_user_data" which when hit in ajax will satisfy first condition in this function
    }


    //here, we validate edit user form data and insert record in the database
    public function update_user(Request $request)
    {
        // Retrieve the user
        $user = User::findOrFail($request->route("id"));

        $user->role = $request->input('user_role');
        $user->save();

        if ($user) {
            // return $user->permissions;
            return back()->with('user_updated_success_message', 'Permissions updated successfully.');
        }
    }



    public function delete_user(Request $request)
    {
        $user = User::find($request->route("id"));
        $user->delete();
        if ($user) {
            return back()->with("user_deleted_success_message", "User deleted successfully");
        }
        return $user;
        // return back();
        // $user = User::where('id', '=', $request->route('id'))->get();
        // if($user->isNotEmpty())
        // {
        //     return view('edit-user', ['user' => $user]);
        // }
        // else
        // {
        //     return view('edit-user', ['user_not_found_error' => "User Not Found!"]);
        // }
    }



    // ========================== Comments ======================================




    public function show_comments(Request $request)
    {
        if ($request->query('show_comments')) {
            $Comments = DB::table('comments')
                ->leftJoin('users', 'comments.user_id', '=', 'users.id')
                ->select('comments.*', 'users.id as user_id', 'users.name as user_name', 'users.profile_image as user_profile_image')
                ->where('comments.task_id', '=', $request->route("task_id"))
                ->where('comments.comment_parent_id', '=', null)
                ->orderBy('comments.created_at', 'asc')
                ->get();

            foreach ($Comments as $comment) {
                $comment->user_profile_image = set_user_image($comment->user_profile_image ?? null);

                // Convert created_at to ISO format for consistent timezone handling in JavaScript
                if (isset($comment->created_at)) {
                    $created_at = new \DateTime($comment->created_at);
                    $comment->created_at = $created_at->format('c'); // ISO 8601 format
                }
            }

            $commentIds = $Comments->pluck('id')->toArray();
            $attachmentsByComment = DB::table('comment_attachments')
                ->whereIn('comment_id', $commentIds)
                ->get()
                ->groupBy('comment_id');

            foreach ($Comments as $comment) {
                $attachments = $attachmentsByComment->get($comment->id, []);

                // Convert attachment timestamps to ISO format
                foreach ($attachments as $attachment) {
                    if (isset($attachment->created_at)) {
                        $created_at = new \DateTime($attachment->created_at);
                        $attachment->created_at = $created_at->format('c'); // ISO 8601 format
                    }
                }

                $comment->commentAttachments = $attachments;
            }

            return ['Comments' => $Comments];
        }

        if ($request->query('get_comments_count')) {
            $comment_count = Comments::whereNull('comments.comment_parent_id')->count();
            if ($comment_count > 0) {
                return $comment_count;
            }
            return 0;
        }
    }





    public function extractCommentBody($html)
    {
        if (empty($html)) {
            return '';
        }

        libxml_use_internal_errors(true);

        $doc = new \DOMDocument();
        $doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));

        $xpath = new \DOMXPath($doc);

        foreach ($xpath->query('//span[contains(@class, "mentioned-user")]') as $node) {
            $node->parentNode->removeChild($node);
        }

        $bodyNode = $doc->getElementsByTagName('body')->item(0);
        $text = $bodyNode ? $bodyNode->textContent : '';

        // Normalize non-breaking space to regular space
        $text = str_replace("\xC2\xA0", ' ', $text);

        return trim($text);
    }



    public function save_comment(Request $request)
    {
        $validate = Validator::make($request->all(), [])->validate();

        $task = Task::where('id', '=', $request->route("task_id"))->first();

        // Handle task status update
        if ($request->has("edit-task-status")) {
            if ($request->has("task_status")) {
                $task->status_id = $request->input("task_status");
            }
        }

        // Handle user assignments
        if ($request->has("edit-assign-to")) {
            if ($request->has("assign_to")) {
                $assignToUserIds = $request->input('assign_to');
                $task->users()->sync($assignToUserIds);
            }
        }

        if ($request->has("edit-due-date")) {
            if ($request->has("due-date")) {
                $task->due_date = $request->input('due-date');
            }
        }

        $task->save();

        if ($request->has("comment-body")) {













            $comment = new Comments();
            $comment->user_id = Auth::user()->id;
            $comment->task_id = $request->route("task_id");
            $comment->comment_parent_id = $request->has("comment_parent_id") ? $request->input("comment_parent_id") : null;
            $comment->comment_body = $request->input('comment-body');
            $comment->save();

            // Handle attachments
            if ($request->hasFile('comment_attachments')) {
                $attachments = $request->file('comment_attachments');
                $uploadPath = 'public/uploads';

                if (!Storage::exists($uploadPath)) {
                    Storage::makeDirectory($uploadPath, 0755, true);
                }

                if (is_array($attachments)) {
                    foreach ($attachments as $index => $file) {
                        $extension = $file->getClientOriginalExtension();
                        $filename = time() . '(' . ($index + 1) . ')' . '.' . $extension;
                        $fullPath = $uploadPath . '/' . $filename;

                        if (!Storage::exists($fullPath)) {
                            $path = $file->storeAs('uploads', $filename, 'public');
                            $comment->commentAttachments()->create([
                                'comment_id' => $comment->id,
                                'comment_attachment' => $filename,
                            ]);
                        }
                    }
                }
            }

            // Handle mentions and send notifications


            $commentHtml = $request->input('comment-body');

            $cleanText = strip_tags(str_replace('&nbsp;', ' ', $request->input('comment-body')));


            $mentionedUserIds = $request->input('mentioned_users', []);


            if (empty($mentionedUserIds)) {
                if ($request->has('task_id')) {
                    $task = Task::findOrFail($request->input('task_id'));


                    $task->users->each(function ($user) use ($cleanText, $task) {
                        if ($user->id !== Auth::id()) {
                            $user->notify(new CommentAddedNotification($cleanText, Auth::user(), $task));
                        }
                    });


                    if ($task->admin && $task->admin->id !== Auth::id() && !$task->users->contains('id', $task->admin->id)) {
                        $task->admin->notify(new CommentAddedNotification($cleanText, Auth::user(), $task));
                    }
                }
            }



            $mentionedUserIds = array_unique($mentionedUserIds);

            if ($request->has('task_id')) {
                $task = Task::findOrFail($request->input('task_id'));
                $notifiedMentionedIds = [];

                // Notify mentioned users
                foreach ($mentionedUserIds as $userId) {
                    $mentionedUser = User::find($userId);
                    if ($mentionedUser && $mentionedUser->id !== Auth::id()) {
                        $mentionedUser->notify(new MentionedInComment($cleanText, Auth::user(), $task));
                        $notifiedMentionedIds[] = $mentionedUser->id;
                    }
                }


                $task->users->each(function ($user) use ($cleanText, $task, $notifiedMentionedIds) {
                    if ($user->id !== Auth::id() && !in_array($user->id, $notifiedMentionedIds)) {
                        $user->notify(new CommentAddedNotification($cleanText, Auth::user(), $task));
                    }
                });


                if (
                    $task->admin &&
                    $task->admin->id !== Auth::id() &&
                    !in_array($task->admin->id, $notifiedMentionedIds) &&
                    !$task->users->contains('id', $task->admin->id)
                ) {
                    $task->admin->notify(new CommentAddedNotification($cleanText, Auth::user(), $task));
                }
            }







            // Broadcast the event after everything is saved
            event(new CommentAdded($comment, $request->route('task_id')));

            if ($comment) {
                return ['status' => 'success', 'comment-saved-success-message' => "Comment saved successfully!"];
            }
        }
    }



    public function edit_comment(Request $request, $comment_id)
    {
        $comment = Comments::find($comment_id);

        if (!$comment) {
            return response()->json([
                'success' => false,
                'message' => 'Comment not found.'
            ], 404);
        }

        if ($comment->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to edit this comment.'
            ], 403);
        }

        $comment->comment_body = $request->input('edit-comment-input');

        if ($comment->save()) {
            return response()->json([
                'success' => true,
                'message' => 'Comment updated successfully.',
                'comment' => $comment
            ]);
        } else {

            Log::error('Failed to save comment.', ['comment_id' => $comment_id]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update comment. Please try again.'
            ], 500);
        }
    }


    public function delete_comment(Request $request)
    {
        $comment = Comments::find($request->input('comment_id'));

        // Check if comment exists and belongs to the current user
        if ($comment && $comment->user_id === Auth::id()) {
            // Delete any associated attachments first
            if ($comment->commentAttachments) {
                foreach ($comment->commentAttachments as $attachment) {
                    Storage::delete('public/uploads/' . $attachment->comment_attachment);
                }
            }

            // Delete the comment
            if ($comment->delete()) {
                return response()->json(['status' => 'success', 'message' => 'Comment deleted successfully']);
            }
        }

        return response()->json(['status' => 'error', 'message' => 'You can only delete your own comments'], 403);
    }

    public function show_replies(Request $request)
    {
        if ($request->route('comment_id')) {
            $childComments = DB::table('comments')
                ->leftJoin('users', 'comments.user_id', '=', 'users.id')
                ->select('comments.*', 'users.id as user_id', 'users.name as user_name')
                ->where('comment_parent_id', '=', $request->route("comment_id"))
                ->orderBy('comments.created_at', 'desc');

            $childCommentsData = $childComments->get();

            // Convert timestamps to ISO format for consistent timezone handling
            foreach ($childCommentsData as $comment) {
                if (isset($comment->created_at)) {
                    $created_at = new \DateTime($comment->created_at);
                    $comment->created_at = $created_at->format('c'); // ISO 8601 format
                }
            }

            $childCommentsCount = $childComments->count();
            return ['childCommentsData' => $childCommentsData, 'childCommentsCount' => $childCommentsCount];
        }
    }

    public function save_reply(Request $request)
    {
        $comment = Comments::find($request->input("comment_id"));
        if ($comment) {
            $reply = new Comments();
            $reply->task_id = $comment->task_id;
            $reply->user_id = $comment->user_id;
            $reply->comment_parent_id = $request->input('comment_id');
            $reply->comment_body = $request->input("comment-reply-input");
            if ($reply->save()) {
                return response()->json(['success' => true, 'message' => 'Reply saved successfully.']);
            } else {
                return response()->json(['success' => false, 'message' => 'Failed to save reply.']);
            }
        } else {
            return response()->json(['success' => false, 'message' => 'Comment not found.']);
        }
    }

    public function getChildComments(Request $request)
    {
        $commentId = $request->route("comment_id");

        $comments = $this->getNestedComments($commentId);

        return response()->json(['childCommentsData' => $comments]);
    }

    private function getNestedComments($parentId)
    {
        $comments = DB::table('comments')
            ->leftJoin('users', 'comments.user_id', '=', 'users.id')
            ->select('comments.*', 'users.id as user_id', 'users.name as user_name')
            ->where('comment_parent_id', '=', $parentId)
            ->orderBy('comments.created_at', 'desc')
            ->get();

        foreach ($comments as $comment) {
            // Convert timestamps to ISO format for consistent timezone handling
            if (isset($comment->created_at)) {
                $created_at = new \DateTime($comment->created_at);
                $comment->created_at = $created_at->format('c'); // ISO 8601 format
            }

            $comment->nestedReplies = $this->getNestedComments($comment->id);
        }

        return $comments;
    }


    // ======================= Status Hub ==========================
    public function statushub()
    {
        $user = Auth::user();

        $phases = Phase::with(['projects' => function ($query) {
            $query->withPivot('project_duration', 'project_target');
        }, 'categories'])->get();


        $projects = Project::with(['phases' => function ($query) {
            $query->withPivot('project_duration', 'project_target');
        }])->get();


        return view('statushub.index', compact('phases', 'projects'));
    }
}
